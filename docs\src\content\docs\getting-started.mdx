---
title: 快速上手
description: 快速上手
---

## 系统要求

- 最新版本的 Chrome、Edge 或其他基于 Chromium 内核的浏览器。
- 最新版本的 Firefox（含安卓版）。

:::note
此扩展以 Chrome 为基础开发，主要支持 Chrome 以及 Chromium 内核的浏览器。

Firefox 版由于 API 限制，部分功能无法使用，而且可能BUG更多。

Firefox 安卓版只保证能用，由于UI主要是为桌面版设计的，所以在手机上体验可能不太好。
:::

## 安装

### Chrome

包括 Chrome、Edge、Opera 等基于 Chromium 内核的浏览器。

从 [Chrome 应用商店](https://chromewebstore.google.com/detail/danmaku-anywhere/jnflbkkmffognjjhibkjnomjedogmdpo?hl=zh) 下载安装

使用商店安装可以自动更新，以及多设备同步设置。

### Firefox

从 [Firefox Add-ons](https://addons.mozilla.org/zh-CN/firefox/addon/danmaku-anywhere/)下载安装

其他安装方式见 [安装说明](/docs/install)

## 首次使用

安装扩展后刷新网页，左下角会出现控件的悬浮按钮：
![弹幕图标](../../assets/ui/fab.png)
看到此图标表示扩展成功启用。

扩展默认在所有网页上生效，如果希望只在部分网页上生效，可以在[装填配置](/docs/mount-profile)中修改配置。

## 使用流程

### 1. 获取弹幕

#### [内置搜索](/docs/danmaku#内置搜索)

在搜索页输入关键词，然后点击搜索按钮，即可搜索到相关的弹幕，点击弹幕即可下载。

支持的弹幕来源有

- 弹弹Play
- B站
- 腾讯视频

其中**弹弹Play**为默认开启的，其余的需要在设置页中开启。

部分搜索结果受登录状态，地域等因素影响

#### [地址解析](/docs/danmaku#地址解析)

在搜索页选择**地址解析**标签页，输入视频地址，然后点击解析按钮。

支持B站和腾讯视频的影视类视频。

#### [上传本地弹幕](/docs/danmaku#用户上传)

也可以上传本地的弹幕文件（一般为 .xml）

如果发现上传的弹幕文件解析失败，可能是尚不支持这个格式，请提交 [功能请求](https://github.com/Mr-Quin/danmaku-anywhere/issues/new)。

### 2. 加载弹幕

有了弹幕后，在悬浮窗口中的“装填弹幕”页中可以查看，点击“装填”按钮即可加载弹幕。此时控件按钮会变绿，播放视频即可看到弹幕。

### 3. 自动化

手动搜索和装填弹幕的操作比较繁琐，[适配规则](/integration-policy/overview)可以将这两个步骤自动化。

## 问题反馈

- [Github Issues](https://github.com/Mr-Quin/danmaku-anywhere/issues)
- QQ 群：531237584