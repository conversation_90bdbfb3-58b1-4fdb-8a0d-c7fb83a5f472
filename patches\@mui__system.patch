diff --git a/esm/useMediaQuery/useMediaQuery.js b/esm/useMediaQuery/useMediaQuery.js
index c020b8eb1900903bf8e52d6b057ed1aa725a970b..b939208955e8d800661e1c08c1c1b6a0fd88eb1d 100644
--- a/esm/useMediaQuery/useMediaQuery.js
+++ b/esm/useMediaQuery/useMediaQuery.js
@@ -87,7 +87,7 @@ export function unstable_createUseMediaQuery(params = {}) {
     const supportMatchMedia = typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined';
     const {
       defaultMatches = false,
-      matchMedia = supportMatchMedia ? window.matchMedia : null,
+      matchMedia = supportMatchMedia ? window.matchMedia.bind(window) : null,
       ssrMatchMedia = null,
       noSsr = false
     } = getThemeProps({
diff --git a/modern/useMediaQuery/useMediaQuery.js b/modern/useMediaQuery/useMediaQuery.js
index c020b8eb1900903bf8e52d6b057ed1aa725a970b..b939208955e8d800661e1c08c1c1b6a0fd88eb1d 100644
--- a/modern/useMediaQuery/useMediaQuery.js
+++ b/modern/useMediaQuery/useMediaQuery.js
@@ -87,7 +87,7 @@ export function unstable_createUseMediaQuery(params = {}) {
     const supportMatchMedia = typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined';
     const {
       defaultMatches = false,
-      matchMedia = supportMatchMedia ? window.matchMedia : null,
+      matchMedia = supportMatchMedia ? window.matchMedia.bind(window) : null,
       ssrMatchMedia = null,
       noSsr = false
     } = getThemeProps({
