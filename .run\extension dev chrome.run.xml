<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="extension dev chrome" type="js.build_tools.npm" singleton="false">
    <package-json value="$PROJECT_DIR$/packages/danmaku-anywhere/package.json" />
    <command value="run" />
    <scripts>
      <script value="dev" />
    </scripts>
    <node-interpreter value="project" />
    <envs />
    <method v="2" />
  </configuration>
</component>