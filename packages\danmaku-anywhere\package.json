{"name": "@mr-quin/danmaku-anywhere", "version": "1.0.3", "private": true, "description": "Browser extension for danmaku-anywhere", "keywords": [], "author": "<PERSON><PERSON><PERSON><PERSON>", "repository": "https://github.com/Mr-Quin/danmaku-anywhere", "license": "AGPL-3.0", "type": "module", "scripts": {"dev": "vite", "dev:firefox": "VITE_TARGET_BROWSER=firefox pnpm run dev", "start:firefox": "web-ext run --source-dir ./build --devtools -p ./extention-profile --profile-create-if-missing --keep-profile-changes --pref extensions.dnr.feedback=true", "start:firefox-android": "pnpm run start:firefox -t firefox-android --firefox-apk org.mozilla.firefox_beta --pref extensions.dnr.feedback=true", "build": "vite build --outDir build --emptyOutDir", "build:firefox": "VITE_TARGET_BROWSER=firefox pnpm run build", "test": "vitest", "preview": "vite preview", "lint": "tsc && biome check --fix", "format": "biome format --write", "package": "node ./scripts/package.js", "package:firefox": "VITE_TARGET_BROWSER=firefox pnpm run package", "tsc": "tsc"}, "dependencies": {"@danmaku-anywhere/danmaku-converter": "workspace:*", "@danmaku-anywhere/danmaku-engine": "workspace:*", "@danmaku-anywhere/danmaku-provider": "workspace:*", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.6", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.1", "@mui/lab": "7.0.0-beta.13", "@mui/material": "^7.1.1", "@mui/system": "^7.1.1", "@mui/x-data-grid": "^8.5.2", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "@tanstack/react-virtual": "^3.13.10", "@use-gesture/react": "^10.3.1", "dexie": "^4.0.11", "i18next": "^25.2.1", "immer": "^10.1.1", "jszip": "^3.10.1", "opencc-js": "^1.0.5", "pinyin-pro": "^3.26.0", "react": "19.1.0", "react-dom": "19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.58.1", "react-hotkeys-hook": "^5.1.0", "react-i18next": "^15.5.3", "react-markdown": "^10.1.0", "react-router": "^7.6.2", "ts-pattern": "^5.7.1", "urlpattern-polyfill": "^10.1.0", "zod": "^3.25.67", "zustand": "5.0.5"}, "devDependencies": {"@crxjs/vite-plugin": "2.0.2", "@types/chrome": "^0.0.326", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/wicg-file-system-access": "^2023.10.6", "@vitejs/plugin-react": "^4.5.2", "vite": "^6.3.5", "vitest": "^3.2.4", "web-ext": "^8.7.1"}}