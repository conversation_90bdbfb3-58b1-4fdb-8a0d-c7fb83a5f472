---
title: 安装
description: 安装
---

## Chrome（Chromium 内核浏览器）

作者主要使用 Chrome 浏览器，安装说明基于 Chrome 浏览器，其他浏览器可能会有不同。

从 [Chrome 应用商店](https://chromewebstore.google.com/detail/danmaku-anywhere/jnflbkkmffognjjhibkjnomjedogmdpo?hl=zh) 下载安装。

使用商店安装可以享受自动更新和多设备同步配置等功能。

## Firefox（含安卓版）

从 [Firefox Add-ons](https://addons.mozilla.org/zh-CN/firefox/addon/danmaku-anywhere/)下载安装。

## 手动安装

除非有特殊需求，不推荐手动安装。

**Chrome**
1. 从 [Github](https://github.com/Mr-Quin/danmaku-anywhere/releases/latest) 下载最新版本。
2. 解压到一个文件夹。
3. 进入 [扩展页面](chrome://extensions/)，启用开发者模式，点击“加载未打包的扩展”，选择已解压的扩展文件夹。
4. 后续更新时，直接解压到该文件夹并覆盖，刷新扩展即可。

:::note
安装后，除非删除扩展，请勿删除该文件夹。
:::

**Firefox**

Firefox手动安装比较麻烦，有兴趣的参考官方文档。
- [1](https://extensionworkshop.com/documentation/develop/temporary-installation-in-firefox/)
- [2](https://extensionworkshop.com/documentation/publish/signing-and-distribution-overview/)
