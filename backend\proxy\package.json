{"name": "@danmaku-anywhere/proxy", "version": "0.1.0", "private": true, "description": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "repository": "https://github.com/Mr-Quin/danmaku-anywhere", "license": "MIT", "type": "module", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "dev:remote": "pnpm dev --remote", "test": "vitest", "lint": "biome check --fix", "cf-typegen": "wrangler types"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.41", "@types/node": "^24.0.3", "typescript": "^5.8.3", "vitest": "3.1.0", "wrangler": "^4.20.3"}, "dependencies": {"@google/generative-ai": "^0.24.1", "zod": "^3.22.4", "@hono/zod-validator": "^0.7.0", "hono": "^4.7.11"}}