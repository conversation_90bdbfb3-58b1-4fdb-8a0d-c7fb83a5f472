---
title: XPath规则
description: XPath规则
---

基于规则的，可自定义的适配方案，用于在网页上采集番剧信息从而实现弹幕的自动匹配。

:::tip
v0.19.0中新增[AI自动适配](./ai)，推荐使用。
:::

## 基本概念

### 番剧信息

弹幕的自动匹配需要 4 个基本信息

适配规则允许用户为单独为每个信息提供`XPath`和`regex`

##### 番剧标题

**必须**

当前播放的番剧的名称。作为关键词用于搜索番剧信息。

##### 集数

**必须**

当前播放的是第几集。非分集视频（如电影）可忽略。默认为 1。分集视频则**必须**提供，否则无法正确匹配弹幕。

必须为阿拉伯数字，不支持非数字的集数，如“第一集”。

用于从检索到的番剧信息中选择对应的集数。

##### 季

**可选**

当前播放的是第几季。存在时，作为关键词的一部分用于搜索番剧信息。搜索时会将与番剧标题拼接。

例（加粗为季）：

- 进击的巨人 **第二季**
- 进击的巨人 **最终季 完结篇**

大多数网站的番剧标题中包含了季信息，这种情况下可以忽略。

然而也有网站会将季信息提取出来独立在标题外，这时就需要单独提供季的`XPath`和`regex`。

##### 单集标题

**可选**

当前播放的单集的标题。非分集视频默认为番剧标题。辅助匹配。

<hr />

以这个标题为例：

```
【我推的孩子】 第二季 第01集 情感演技
```

四个信息分别为：

- 番剧标题：【我推的孩子】
- 季：第二季
- 集数：01（转换为数字时为 1）
- 单集标题：情感演技

或者

- 番剧标题：【我推的孩子】 第二季
- 季：(空)
- 集数：01
- 单集标题：情感演技

这两种是等价的，选择编写简单的即可。

### 规则

为了提取这些信息，需要根据网页的结构编写规则

规则是`XPath`和正则表达式`regex`的组合，`XPath`用于选择信息所在的**节点**，`regex`用于从节点的文本中提取信息。

扩展会监视选择到的节点，当节点的文本发生变化时，扩展会重新提取信息，以此判断是否需要更新弹幕。

一个适配规则可以包含至多 4 个规则，分别对应上述 4 个番剧信息。每个规则可以包含多条`XPath`和`regex`。

**注意事项**

- 适配规则需要和装填配置关联（装填配置中选择）
- 适配规则只在存在视频时生效
- `regex`固定包含`i`标志，表示忽略大小写
- `XPath`和`regex`存在多个时，取第一个成功匹配的
- 如果`XPath`选择到多个节点，取第一个
- `XPath`需要选择一个**节点**，不支持如`concat(//*[@id="title"],"")`这样的输出字符串的`XPath`
- `XPath`可以从浏览器的开发者工具中复制。复制得到的`XPath`不一定能够覆盖所有情况，可能需要手动调整。

## 编写规则

:::caution
编写规则有一定门槛，请确保您对`XPath`和正则表达式`regex`有基础的了解。
:::

由于网页的结构各不相同，适配规则需要根据网页的结构编写。

这里考虑两种情况：信息全部在**一个节点**中和信息分散在**多个节点**中。

### 单个节点

默认，也是最普遍的情况。所有信息都存在于一个节点中，这个节点通常是标题。

在表单中表现为勾选 _仅使用标题匹配_ 选项。

#### 举个简单的例子

假设网页上有这样一个标题：

<div>
  <p
    id="anime-title-1"
    class="title"
    style={{
      backgroundColor: '#e6e6e6',
      color: '#000',
      padding: '10px',
      borderRadius: '5px',
      width: 'fit-content',
    }}
  >
    败犬女主太多了！ 第03集 在战斗开始前就输了
  </p>
</div>

对应`HTML`

```html
<div id="anime-title-1" class="title">
  <p>败犬女主太多了！ 第03集 在战斗开始前就输了</p>
</div>
```

这个标题包含了番剧标题、集数和单集标题。

从这个标题中提取信息可以使用以下`XPath`：

```text
//*[@id="anime-title-1"]
```

和以下`regex`：

```regex
(?<title>.+) 第(?<episode>\d+)集 (?<episodeTitle>.*)
```

提取的信息为：

```json
{
  "title": "败犬女主太多了！",
  "episode": "03",
  "episodeTitle": "在战斗开始前就输了"
}
```

:::note
此处用到了命名捕获组。在单个节点的情况下，必须使用命名捕获组来告诉扩展哪个捕获组对应哪个信息。

| **信息类型** | **命名捕获组**      |
| ------------ | ------------------- |
| 番剧标题     | `(?<title>)`        |
| 集数         | `(?<episode>)`      |
| 季           | `(?<season>)`       |
| 单集标题     | `(?<episodeTitle>)` |

:::

<hr />

#### 再举一个稍复杂的例子：

<div
  id="anime-title-2"
  className="anime-title"
  data-title={'败犬女主太多了！ 第03集 在战斗开始前就输了'}
>
  <p
    style={{
      backgroundColor: '#e6e6e6',
      color: '#000',
      padding: '10px',
      borderRadius: '5px',
      display: 'inline-flex',
      gap: '4px',
    }}
  >
    <span style={{ fontWeight: 'bold' }}>败犬女主太多了！</span>
    <span style={{ color: '#999' }}>第03集</span>
    <span>在战斗开始前就输了</span>
  </p>
</div>

对应`HTML`

```html
<div
  id="anime-title-2"
  class="anime-title"
  data-title="败犬女主太多了！ 第03集 在战斗开始前就输了"
>
  <p>
    <span>败犬女主太多了！</span>
    <span>第03集</span>
    <span>在战斗开始前就输了</span>
  </p>
</div>
```

可以发现，虽然信息分散在多个`span`中，但是这些`span`都在同一个`p`和`div`中，选择`div`即可从文本中提取所有信息。
和上面的例子的区别在于这里的空格并非空格符，而是`CSS`样式，需调整`regex`。

另外可以发现`div`中有一个`data-title`属性，这个属性包含了所有信息，也可以选择这个属性。

```
//div/@data-title
```

基于这个原则，理论上所有的网页都可以理解为一个节点，只需要选择最小的包含所有信息的节点，然后使用`regex`提取信息即可。
当然这样可能会导致`regex`变得复杂，所以还可以选择使用**多个节点**的配置。

### 多个节点

即信息分散在多个节点中，这种情况下可以单独提供每个信息的`XPath`和`regex`。

在表单中表现为取消勾选 _仅使用标题匹配_ 选项。

接着用上面的例子

```html
<div
  id="anime-title-2"
  class="anime-title"
  data-title="败犬女主太多了！ 第03集 在战斗开始前就输了"
>
  <p>
    <span>败犬女主太多了！</span>
    <span>第03集</span>
    <span>在战斗开始前就输了</span>
  </p>
</div>
```

可以提供以下`XPath`和`regex`：

**标题**

```text
//*[@id="anime-title-2"]/p/span[1]
```

```regex
.*
```

**集数**

```text
//*[@id="anime-title-2"]/p/span[2]
```

```regex
第(\d+)集
```

**单集标题**

```text
//*[@id="anime-title-2"]/p/span[3]
```

```regex
.*
```

此例子中默认**季**存在于标题中，所以不需要提供季的`XPath`和`regex`。

:::note
在多个节点的情况下，不要使用命名捕获组。可以使用捕获组`()`和非捕获组`(?:)`来忽略不需要的信息。如果存在多个捕获组，取第一个。
:::

<hr />
