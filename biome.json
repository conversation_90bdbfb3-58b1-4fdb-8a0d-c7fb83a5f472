{"$schema": "https://biomejs.dev/schemas/2.0.0/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": true}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSpacing": true}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": false, "a11y": {}, "complexity": {"noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error", "noAdjacentSpacesInRegex": "error", "noArguments": "error"}, "correctness": {"noUnusedImports": "error", "noChildrenProp": "error", "noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidBuiltinInstantiation": "error", "noInvalidConstructorSuper": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedPrivateClassMembers": "error", "noUnusedVariables": "error", "useIsNan": "error", "useJsxKeyInIterable": "error", "useValidForDirection": "error", "useYield": "error", "useValidTypeof": "error"}, "security": {"noDangerouslySetInnerHtmlWithChildren": "error", "noBlankTarget": "error"}, "style": {"noInferrableTypes": "error", "noNamespace": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useConsistentArrayType": "error", "useForOf": "error", "useShorthandFunctionType": "error", "noNonNullAssertion": "warn", "useConst": "error", "useImportType": "error", "useArrayLiterals": "off", "noParameterAssign": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noUselessElse": "error"}, "suspicious": {"noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateJsxProps": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "warn", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noSparseArray": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "useNamespaceKeyword": "error", "noWith": "error", "noVar": "error"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true}, "globals": []}, "overrides": [{"includes": ["**/*.test.ts"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off", "noEmptyBlockStatements": "off"}}}}]}