{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "baseUrl": "./packages", "paths": {"@danmaku-anywhere/danmaku-engine": ["danmaku-engine/src"], "@danmaku-anywhere/danmaku-provider": ["danmaku-provider/src"]}}}