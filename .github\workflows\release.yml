name: Release Package

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    strategy:
      matrix:
        node-version: [ 20.x ]

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        # uses version from package.json > packageManager

      - name: Install and build dependencies
        run: |
          pnpm install
          pnpm build
        env:
          VITE_PROXY_ORIGIN: ${{ vars.VITE_PROXY_ORIGIN }}
          VITE_PROXY_URL: ${{ vars.VITE_PROXY_URL }}

      - name: Package
        run: |
          cd packages/danmaku-anywhere
          pnpm package
          pnpm build:firefox
          pnpm package:firefox
        env:
          VITE_PROXY_ORIGIN: ${{ vars.VITE_PROXY_ORIGIN }}
          VITE_PROXY_URL: ${{ vars.VITE_PROXY_URL }}

      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: package-files
          path: packages/danmaku-anywhere/package/*.zip

  publish-release:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      contents: write # required by ncipollo/release-action@v1
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')

    steps:
      - name: Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: package-files
          path: packages

      - name: Print current directory
        run: |
          ls -al
          pwd

      - name: Publish Release
        uses: ncipollo/release-action@v1
        with:
          allowUpdates: true
          artifacts: "packages/*"
          generateReleaseNotes: true
          makeLatest: true

  publish-chrome:
    runs-on: ubuntu-latest
    needs: build
    environment: chrome-web-store-publish
    # Only run this job if the push event is a tag push
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')

    steps:
      - name: Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: package-files
          path: packages

      - name: Print current directory
        run: |
          ls -al
          pwd

      - name: Publish to Chrome Web Store
        uses: mnao305/chrome-extension-upload@v5.0.0
        with:
          file-path: "packages/*chrome.zip"
          extension-id: ${{ vars.EXTENSION_ID }}
          client-id: ${{ secrets.CLIENT_ID }}
          client-secret: ${{ secrets.CLIENT_SECRET }}
          refresh-token: ${{ secrets.REFRESH_TOKEN }}
          glob: true
          publish: false

  publish-firefox:
    runs-on: ubuntu-latest
    needs: build
    environment: firefox-addon-publish
    steps:
      - name: Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: package-files
          path: packages

      - name: Print current directory
        run: |
          ls -al
          pwd

      - uses: wdzeng/firefox-addon@v1
        name: Upload to Mozilla Add-ons
        with:
          addon-guid: ${{ vars.ADDON_ID }}
          xpi-path: "packages/*firefox.zip"
          self-hosted: false
          jwt-issuer: ${{ secrets.FIREFOX_JWT_ISSUER }}
          jwt-secret: ${{ secrets.FIREFOX_JWT_SECRET }}
          compatibility: firefox,android