{"name": "@danmaku-anywhere/plex-danmaku", "private": true, "version": "0.0.1", "type": "module", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "A danmaku script for Plex", "keywords": ["plex", "danmaku", "user script"], "repository": "https://github.com/Mr-<PERSON><PERSON>/plex-danmaku", "license": "MIT", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "tsc && eslint --ext .js,.jsx,.ts,.tsx --fix ."}, "dependencies": {"zustand": "4.5.2", "@danmaku-anywhere/danmaku-engine": "workspace:*", "@danmaku-anywhere/dandanplay-api": "workspace:*", "danmaku": "2.0.6", "idb": "7.1.1", "preact": "10.13.2"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "7.23.4", "@preact/preset-vite": "2.5.0", "sass": "1.62.0", "vite-plugin-monkey": "3.1.1", "vite": "^4.5.3", "vite-plugin-eslint": "^1.8.1"}}