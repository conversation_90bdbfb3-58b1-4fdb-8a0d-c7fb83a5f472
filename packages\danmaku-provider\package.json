{"name": "@danmaku-anywhere/danmaku-provider", "version": "0.1.0", "private": true, "description": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "repository": "https://github.com/Mr-Quin/danmaku-anywhere", "license": "MIT", "type": "module", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "exports": {"./ddp": {"types": "./dist/src/providers/ddp/index.d.ts", "default": "./dist/src/providers/ddp/index.js"}, "./bilibili": {"types": "./dist/src/providers/bilibili/index.d.ts", "default": "./dist/src/providers/bilibili/index.js"}, "./tencent": {"types": "./dist/src/providers/tencent/index.d.ts", "default": "./dist/src/providers/tencent/index.js"}, "./genAi": {"types": "./dist/src/providers/genAi/index.d.ts", "default": "./dist/src/providers/genAi/index.js"}}, "scripts": {"test": "vitest", "copy": "copyfiles -u 1 src/**/protobuf/** dist/src", "protobuf:compile": "pbjs -t static-module -w es6 -o src/protobuf/protobuf.js src/protobuf/definition/bilibili.dm.proto && pbts -o src/protobuf/protobuf.d.ts src/protobuf/protobuf.js", "build": "pnpm protobuf:compile && tsc && pnpm copy", "lint": "tsc && biome check --fix", "dev": "tsc -w", "openapi-ts": "openapi-ts"}, "dependencies": {"@danmaku-anywhere/danmaku-converter": "workspace:*", "protobufjs": "^7.5.3", "zod": "^3.25.67"}, "devDependencies": {"@hey-api/openapi-ts": "^0.73.0", "copyfiles": "^2.4.1", "protobufjs-cli": "^1.1.3", "vitest": "^3.2.4"}}