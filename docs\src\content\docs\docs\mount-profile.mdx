---
title: 装填配置
description: 装填配置
---

装填配置是扩展的核心配置，用于指定扩展在哪些网页上生效，以及如何将弹幕装填到视频上。

同一网址最多只能有一个装填配置生效，如果有多个装填配置匹配到同一个网址，扩展会选择第一个匹配到的装填配置。

装填配置需要以下信息：

## 名称

就是一个名字，方便识别。

## 视频节点

视频节点的选择器，用于指定视频节点。扩展会将弹幕覆盖到这个节点上并与其同步。

默认为`video`，基本没有改的必要。

## 适配

用于关联[适配规则](/docs/integration-policy)。

## 网址（URL 模式）

相当于一个白名单，只有在这些网址上扩展才会生效。

装填配置对应的网址不是单一的地址，而是一个[**模式**](https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/Match_patterns)，可以匹配多个网址。

:::tip
基本上，如果希望匹扩展在`https://www.example.com`上生效，那么模式填写 `https://www.example.com/*` 或者 `https://*.example.com/*` 即可。
:::

拿 B站举例：

| 模式                         | 可以匹配的网址                                                                                                                                         | 说明                                          |
| ---------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------- |
| `https://www.bilibili.com/`  | `https://www.bilibili.com/`                                                                                                                            | 仅匹配 B站首页                               |
| `https://www.bilibili.com/*` | `https://www.bilibili.com/` <br></br> `https://www.bilibili.com/video/BV1FrCqYXEas`                                                                    | 匹配所有`https://www.bilibili.com/`开头的网址 |
| `https://*.bilibili.com/*`   | `https://www.bilibili.com/` <br></br> `https://space.bilibili.com/114514` <br></br> `https://live.bilibili.com/35` <br></br> `https://t.bilibili.com/` | 匹配所有 B站子域名，包括动态、空间、直播等   |

其他例子：

- ip 地址：`http://********/*`
- 含端口：`http://********:8080/*`
- 任意端口：`http://********:*/*`
- 本地服务器：`http://localhost/*`
- 本地`mp4`文件（注意三斜杠）：`file:///*.mp4`
  - 需要在扩展的设置中启用本地文件访问权限
- 任意 HTTPS 地址：`https://*/*`

:::note
模式结尾必须包含 `/`，比如 `/`、`/*`、`/abc`，否则会提示格式错误。
:::

单个装填配置可以填写多个模式。
