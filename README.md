<div align="center">
  <img width="128" height="128" src="./assets/logo.png">
  <h1>
    Danmaku Anywhere
    <p align="center">
      <a href="https://github.com/Mr-Quin/danmaku-anywhere/releases">
        <img alt="GitHub Release" src="https://img.shields.io/github/v/release/Mr-Quin/danmaku-anywhere?style=flat-square&logo=github">
      </a>
      <a href="https://github.com/Mr-Quin/danmaku-anywhere/actions">
        <img alt="GitHub Actions Workflow Status" src="https://img.shields.io/github/actions/workflow/status/Mr-Quin/danmaku-anywhere/release.yml?style=flat-square&logo=github">
      </a>
      <a href="https://chromewebstore.google.com/detail/danmaku-anywhere/jnflbkkmffognjjhibkjnomjedogmdpo?hl=zh">
        <img alt="Chrome Web Store Rating" src="https://img.shields.io/chrome-web-store/rating/jnflbkkmffognjjhibkjnomjedogmdpo?style=flat-square&logo=googlechrome&logoColor=yellow">
      </a>
      <img alt="QQ群" src="https://img.shields.io/badge/QQ%E7%BE%A4-531237584-blue?logo=qq&style=flat-square">
    </p>
  </h1>
</div>


> 没有弹幕怎么看番？
> Danmaku Anywhere是一个可以在任何视频网站上加载弹幕的浏览器扩展

## 功能

- 在任何有视频的网页上加载弹幕，彻底解决看番无弹幕的问题
- 从多个弹幕源获取弹幕，目前支持：
  - 弹弹Play
  - B站
  - 腾讯
- 手动导入其他来源的弹幕文件，支持常见的`.xml`格式
- AI自动匹配视频弹幕
- 可自定义的弹幕样式
- 导出本地缓存的弹幕

## 截图

<video src="https://github.com/user-attachments/assets/81703fe1-d04f-42cb-b9ed-35213c75f2e0"></video>

Plex

![Plex](./assets/screenshot_plex.png)

Jellyfin

![Jellyfin](./assets/screenshot_jellyfin.png)

YouTube

![YouTube](./assets/screenshot_youtube.png)

## 安装

### Chrome（Chromium内核浏览器）

直接通过[Chrome Web Store](https://chromewebstore.google.com/detail/danmaku-anywhere/jnflbkkmffognjjhibkjnomjedogmdpo?hl=zh)安装

### Firefox（含安卓）

直接通过[Firefox Add-ons](https://addons.mozilla.org/zh-CN/firefox/addon/danmaku-anywhere/)安装

手动安装及更详细的说明请看 [快速上手](https://danmaku.weeblify.app/getting-started/)

## 开发

见[开发文档](https://danmaku.weeblify.app/development/structure/)

