{"name": "danmaku-anywhere", "version": "0.0.1", "private": true, "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "Danmaku anywhere", "type": "module", "keywords": ["plex", "danmaku", "user script"], "repository": "https://github.com/Mr-Quin/danmaku-anywhere", "scripts": {"build": "pnpm -r -F \"./packages/**\" build", "format": "pnpm -r -F \"./packages/**\" format", "lint": "pnpm -r -F \"./packages/**\" lint", "test": "pnpm -r -F \"./packages/**\" test run"}, "devDependencies": {"@biomejs/biome": "^2.0.0", "typescript": "^5.8.3"}, "pnpm": {"patchedDependencies": {"@mui/system": "patches/@mui__system.patch"}}, "packageManager": "pnpm@10.11.0+sha256.a69e9cb077da419d47d18f1dd52e207245b29cac6e076acedbeb8be3b1a67bd7"}