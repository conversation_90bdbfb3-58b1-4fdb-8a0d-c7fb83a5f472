{"name": "@danmaku-anywhere/danmaku-engine", "version": "0.0.1", "private": true, "description": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "repository": "https://github.com/Mr-Quin/danmaku-anywhere", "license": "MIT", "type": "module", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"test": "vitest", "build": "tsc", "lint": "tsc && biome check --fix", "dev": "tsc -w"}, "dependencies": {"@danmaku-anywhere/danmaku-converter": "workspace:*", "danmu": "^0.15.1"}, "devDependencies": {"vitest": "^3.2.4"}}