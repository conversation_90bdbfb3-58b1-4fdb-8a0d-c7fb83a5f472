export const mockBilibiliUserNotLoggedInResponse = {
  code: -101,
  message: '账号未登录',
  ttl: 1,
  data: {
    isLogin: false,
    wbi_img: {
      img_url:
        'https://i0.hdslb.com/bfs/wbi/7cd084941338484aae1ad9425b84077c.png',
      sub_url:
        'https://i0.hdslb.com/bfs/wbi/4932caff0ff746eab6f01bf08b70ac45.png',
    },
  },
}

export const mockBilibiliUserLoggedInResponse = {
  code: 0,
  message: '0',
  ttl: 1,
  data: {
    isLogin: true,
    // some fields are redacted
    email_verified: 1,
    face: '',
    face_nft: 0,
    face_nft_type: 0,
    level_info: {
      current_level: 6,
      current_min: 28800,
      current_exp: 55911,
      next_exp: '--',
    },
    mid: 1,
    mobile_verified: 1,
    money: 1,
    moral: 1,
    official: {
      role: 0,
      title: '',
      desc: '',
      type: -1,
    },
    officialVerify: {
      type: -1,
      desc: '',
    },
    pendant: {
      pid: 0,
      name: 'name',
      image: 'https://i0.hdslb.com/bfs/garb/open/0.png',
      expire: 0,
      image_enhance: 'https://i0.hdslb.com/bfs/garb/open/0.png',
      image_enhance_frame: '',
      n_pid: 0,
    },
    scores: 0,
    uname: 'user',
    vipDueDate: 0,
    vipStatus: 1,
    vipType: 2,
    vip_pay_type: 1,
    vip_theme_type: 0,
    vip_label: {
      path: '',
      text: '年度大会员',
      label_theme: 'annual_vip',
      text_color: '#FFFFFF',
      bg_style: 1,
      bg_color: '#FB7299',
      border_color: '',
      use_img_label: true,
      img_label_uri_hans: '',
      img_label_uri_hant: '',
      img_label_uri_hans_static: 'https://i0.hdslb.com/bfs/vip/0.png',
      img_label_uri_hant_static:
        'https://i0.hdslb.com/bfs/activity-plat/static/20220614/0/VEW8fCC0hg.png',
    },
    vip_avatar_subscript: 1,
    vip_nickname_color: '#FB7299',
    vip: {
      type: 2,
      status: 1,
      due_date: 1750176000000,
      vip_pay_type: 1,
      theme_type: 0,
      label: {
        path: '',
        text: '年度大会员',
        label_theme: 'annual_vip',
        text_color: '#FFFFFF',
        bg_style: 1,
        bg_color: '#FB7299',
        border_color: '',
        use_img_label: true,
        img_label_uri_hans: '',
        img_label_uri_hant: '',
        img_label_uri_hans_static: 'https://i0.hdslb.com/bfs/vip/0.png',
        img_label_uri_hant_static:
          'https://i0.hdslb.com/bfs/activity-plat/static/20220614/0/0.png',
      },
      avatar_subscript: 1,
      nickname_color: '#FB7299',
      role: 3,
      avatar_subscript_url: '',
      tv_vip_status: 0,
      tv_vip_pay_type: 0,
      tv_due_date: 0,
      avatar_icon: {
        icon_type: 1,
        icon_resource: {},
      },
    },
    wallet: {
      mid: 1,
      bcoin_balance: 0.5,
      coupon_balance: 0,
      coupon_due_time: 0,
    },
    has_shop: false,
    shop_url: '',
    allowance_count: 0,
    answer_status: 0,
    is_senior_member: 0,
    wbi_img: {
      img_url: 'https://i0.hdslb.com/bfs/wbi/0.png',
      sub_url: 'https://i0.hdslb.com/bfs/wbi/0.png',
    },
    is_jury: false,
    name_render: null,
  },
}

export const mockBilibiliBangmumiSearchResponse = {
  code: 0,
  message: '0',
  ttl: 1,
  data: {
    seid: '1611017790732734115',
    page: 1,
    pagesize: 20,
    numResults: 7,
    numPages: 1,
    suggest_keyword: '',
    rqt_type: 'search',
    cost_time: {
      total: '0.017769',
      fetch_lexicon: '0.002867',
      params_check: '0.001853',
      is_risk_query: '0.000136',
      illegal_handler: '0.000012',
      main_handler: '0.011059',
      as_request_format: '0.000225',
      as_request: '0.009965',
      deserialize_response: '0.000133',
      as_response_format: '0.000648',
    },
    exp_list: {
      '5502': true,
      '6605': true,
      '7702': true,
      '9902': true,
      '9924': true,
      '9931': true,
      '9941': true,
      '9961': true,
      '100201': true,
      '100805': true,
      '101505': true,
      '102000': true,
      '102405': true,
      '102801': true,
      '102902': true,
      '103000': true,
      '103103': true,
      '103200': true,
      '103300': true,
      '103400': true,
      '104100': true,
      '104202': true,
      '104400': true,
      '105102': true,
      '105202': true,
      '105400': true,
      '105805': true,
      '106000': true,
      '106201': true,
      '106300': true,
      '106405': true,
      '106503': true,
      '106702': true,
      '106904': true,
      '107001': true,
      '107106': true,
      '107202': true,
      '107403': true,
      '107505': true,
      '107601': true,
      '160002': true,
      '171608': true,
    },
    egg_hit: 0,
    result: [
      {
        type: 'media_bangumi',
        media_id: 1434,
        title: '贫穷姐妹物语',
        org_title: '貧乏姉妹物語',
        media_type: 1,
        cv: '山田今日：坂本真绫\n山田明日：金田朋子\n山田佳子：久川绫\n林源三：麦人\n三枝兰子：平松晶子\n一之仓正男：岸尾大辅\n越后屋金子：进藤尚美\n越后屋银子：小樱悦子\n大城志麻：酒井香奈子\n阿野香苗：白石凉..',
        staff:
          '导演：貝澤幸男\n人物设定：高村和宏\n总作画监督：上野ケン\n色彩设计：小日置知子\n动画制作：東映アニメーション\n编辑：麻生芳弘\n录音：川崎公敬\n音乐：小坂明子\n制作编成：樋口宗久\n制作担任：松坂一光',
        season_id: 1434,
        is_avid: false,
        hit_columns: ['org_title'],
        hit_epids: '',
        season_type: 1,
        season_type_name: '番剧',
        selection_style: 'grid',
        ep_size: 10,
        url: 'https://www.bilibili.com/bangumi/play/ss1434',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 26810,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '1 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26810',
            release_date: '',
            badges: null,
            index_title: '1 ',
            long_title: '浴衣与烟火与苹果糖之日',
          },
          {
            id: 26811,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '2 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26811',
            release_date: '',
            badges: null,
            index_title: '2 ',
            long_title: '房东与西瓜与慰问之日',
          },
          {
            id: 26812,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '3 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26812',
            release_date: '',
            badges: null,
            index_title: '3 ',
            long_title: '胡萝卜与谎言与越后屋姊妹之日',
          },
          {
            id: 26813,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '4 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26813',
            release_date: '',
            badges: null,
            index_title: '4 ',
            long_title: '香水与明日与校外教学之日',
          },
          {
            id: 26814,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '5 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26814',
            release_date: '',
            badges: null,
            index_title: '5 ',
            long_title: '公寓与樱花与搬家之日',
          },
          {
            id: 26815,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '6 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26815',
            release_date: '',
            badges: null,
            index_title: '6 ',
            long_title: '寂寞与银子与姐姐之日',
          },
          {
            id: 26816,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '7 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26816',
            release_date: '',
            badges: null,
            index_title: '7 ',
            long_title: '吃醋与巧克力与情人节之日',
          },
          {
            id: 26817,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '8 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26817',
            release_date: '',
            badges: null,
            index_title: '8 ',
            long_title: '吉他与梦想与唱歌老师之日',
          },
          {
            id: 26818,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '9 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26818',
            release_date: '',
            badges: null,
            index_title: '9 ',
            long_title: '想念与不安与手机之日',
          },
          {
            id: 26819,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/8658f81f75f4fc70c1dab2aaa52ceca9415eb69f.gif',
            title: '10 ',
            url: 'https://www.bilibili.com/bangumi/play/ep26819',
            release_date: '',
            badges: null,
            index_title: '10 ',
            long_title: '感冒与约定与母亲之日',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/36987061a62695bdd48be9468b8ab43d13992442.jpg',
        areas: '日本',
        styles: '萌系/日常/治愈',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss1434',
        desc: '山田今日与山田明日是一对姐妹，她们的母亲从小的时候过世了，父亲欠下庞大的债务后就从此下落不明。两人住在屋龄40年的公寓（没有浴室、房租一个月两万六千日圆），姊姊今日一边做送报纸的工作一边维持生计，妹妹...',
        pubtime: 1151510400,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 9.5,
          user_count: 1037,
        },
        display_info: null,
        pgc_season_id: 1434,
        corner: 2,
        index_show: '全10话',
      },
      {
        type: 'media_bangumi',
        media_id: 4778,
        title: '我家三姐妹',
        org_title: 'うちの3姉妹',
        media_type: 1,
        cv: '小芙：大谷育江\n小思：金井美香\n小千：川田妙子\n妈妈：藤村知可\n爸爸：辻谷耕史\n\n',
        staff: '导演：松本ぶりっつ\n',
        season_id: 4778,
        is_avid: false,
        hit_columns: null,
        hit_epids: '',
        season_type: 1,
        season_type_name: '番剧',
        selection_style: 'grid',
        ep_size: 42,
        url: 'https://www.bilibili.com/bangumi/play/ss4778',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 96960,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '1',
            url: 'https://www.bilibili.com/bangumi/play/ep96960',
            release_date: '',
            badges: null,
            index_title: '1',
            long_title: '第01话 大女儿丢丢',
          },
          {
            id: 96961,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '2',
            url: 'https://www.bilibili.com/bangumi/play/ep96961',
            release_date: '',
            badges: null,
            index_title: '2',
            long_title: '第02话 大女儿的悲剧',
          },
          {
            id: 96962,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '3',
            url: 'https://www.bilibili.com/bangumi/play/ep96962',
            release_date: '',
            badges: null,
            index_title: '3',
            long_title: '第03话 自虐的三女儿',
          },
          {
            id: 96963,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '4',
            url: 'https://www.bilibili.com/bangumi/play/ep96963',
            release_date: '',
            badges: null,
            index_title: '4',
            long_title: '第04话 蕾蕾的坏坏球',
          },
          {
            id: 96964,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '5',
            url: 'https://www.bilibili.com/bangumi/play/ep96964',
            release_date: '',
            badges: null,
            index_title: '5',
            long_title: '第05话 老大的脱线传奇',
          },
          {
            id: 96965,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '6',
            url: 'https://www.bilibili.com/bangumi/play/ep96965',
            release_date: '',
            badges: null,
            index_title: '6',
            long_title: '第06话 极端恶劣的时期',
          },
          {
            id: 96966,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '7',
            url: 'https://www.bilibili.com/bangumi/play/ep96966',
            release_date: '',
            badges: null,
            index_title: '7',
            long_title: '第07话 去游乐场 GO',
          },
          {
            id: 96967,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '8',
            url: 'https://www.bilibili.com/bangumi/play/ep96967',
            release_date: '',
            badges: null,
            index_title: '8',
            long_title: '第08话 妖怪 抿奶嘴小鬼',
          },
          {
            id: 96968,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '9',
            url: 'https://www.bilibili.com/bangumi/play/ep96968',
            release_date: '',
            badges: null,
            index_title: '9',
            long_title: '第09话 社长VS自由人',
          },
          {
            id: 96969,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '10',
            url: 'https://www.bilibili.com/bangumi/play/ep96969',
            release_date: '',
            badges: null,
            index_title: '10',
            long_title: '【我家的三姐妹】搞笑片段 蕾蕾说错话还不承认',
          },
          {
            id: 96970,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '11',
            url: 'https://www.bilibili.com/bangumi/play/ep96970',
            release_date: '',
            badges: null,
            index_title: '11',
            long_title: '第11话 妈妈的一天',
          },
          {
            id: 96971,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '12',
            url: 'https://www.bilibili.com/bangumi/play/ep96971',
            release_date: '',
            badges: null,
            index_title: '12',
            long_title: '第12话 爸爸没事吧_',
          },
          {
            id: 96972,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '13',
            url: 'https://www.bilibili.com/bangumi/play/ep96972',
            release_date: '',
            badges: null,
            index_title: '13',
            long_title: '第13话 三女和那两个',
          },
          {
            id: 96973,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '14',
            url: 'https://www.bilibili.com/bangumi/play/ep96973',
            release_date: '',
            badges: null,
            index_title: '14',
            long_title: '第14话 最近二女儿的玩耍',
          },
          {
            id: 96974,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '15',
            url: 'https://www.bilibili.com/bangumi/play/ep96974',
            release_date: '',
            badges: null,
            index_title: '15',
            long_title: '第15话 夏天的回忆',
          },
          {
            id: 96975,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '16',
            url: 'https://www.bilibili.com/bangumi/play/ep96975',
            release_date: '',
            badges: null,
            index_title: '16',
            long_title: '第16话 社长的败北',
          },
          {
            id: 96976,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '17',
            url: 'https://www.bilibili.com/bangumi/play/ep96976',
            release_date: '',
            badges: null,
            index_title: '17',
            long_title: '第17话 会怎么样呢_烧烤',
          },
          {
            id: 96977,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '18',
            url: 'https://www.bilibili.com/bangumi/play/ep96977',
            release_date: '',
            badges: null,
            index_title: '18',
            long_title: '第18话 向电风扇许愿',
          },
          {
            id: 96978,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '19',
            url: 'https://www.bilibili.com/bangumi/play/ep96978',
            release_date: '',
            badges: null,
            index_title: '19',
            long_title: '第19话 离谱的长女',
          },
          {
            id: 96979,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '20',
            url: 'https://www.bilibili.com/bangumi/play/ep96979',
            release_date: '',
            badges: null,
            index_title: '20',
            long_title: '第20话 月月的神隐',
          },
          {
            id: 96980,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '21',
            url: 'https://www.bilibili.com/bangumi/play/ep96980',
            release_date: '',
            badges: null,
            index_title: '21',
            long_title: '第21话 三女的咒文',
          },
          {
            id: 96981,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '22',
            url: 'https://www.bilibili.com/bangumi/play/ep96981',
            release_date: '',
            badges: null,
            index_title: '22',
            long_title: '第22话 呼啦圈三姐妹',
          },
          {
            id: 96982,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '23',
            url: 'https://www.bilibili.com/bangumi/play/ep96982',
            release_date: '',
            badges: null,
            index_title: '23',
            long_title: '第23话 三姐妹时装表演',
          },
          {
            id: 96983,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '24',
            url: 'https://www.bilibili.com/bangumi/play/ep96983',
            release_date: '',
            badges: null,
            index_title: '24',
            long_title: '第24话 丢丢和地球仪',
          },
          {
            id: 96984,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '25',
            url: 'https://www.bilibili.com/bangumi/play/ep96984',
            release_date: '',
            badges: null,
            index_title: '25',
            long_title: '第25话 二女的假扮店主游戏',
          },
          {
            id: 96985,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '26',
            url: 'https://www.bilibili.com/bangumi/play/ep96985',
            release_date: '',
            badges: null,
            index_title: '26',
            long_title: '第26话 去芭蕾教室',
          },
          {
            id: 96986,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '27',
            url: 'https://www.bilibili.com/bangumi/play/ep96986',
            release_date: '',
            badges: null,
            index_title: '27',
            long_title: '第27话 小女儿的成长',
          },
          {
            id: 96987,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '28',
            url: 'https://www.bilibili.com/bangumi/play/ep96987',
            release_date: '',
            badges: null,
            index_title: '28',
            long_title: '第28话 严厉的老爸',
          },
          {
            id: 96988,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '29',
            url: 'https://www.bilibili.com/bangumi/play/ep96988',
            release_date: '',
            badges: null,
            index_title: '29',
            long_title: '第29话 小孩子游戏',
          },
          {
            id: 96989,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '30',
            url: 'https://www.bilibili.com/bangumi/play/ep96989',
            release_date: '',
            badges: null,
            index_title: '30',
            long_title: '第30话 新手妈妈奋斗记',
          },
          {
            id: 96990,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '31',
            url: 'https://www.bilibili.com/bangumi/play/ep96990',
            release_date: '',
            badges: null,
            index_title: '31',
            long_title: '第31话 三姐妹的战争',
          },
          {
            id: 96991,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '32',
            url: 'https://www.bilibili.com/bangumi/play/ep96991',
            release_date: '',
            badges: null,
            index_title: '32',
            long_title: '第32话 接孩子的时间',
          },
          {
            id: 96992,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '33',
            url: 'https://www.bilibili.com/bangumi/play/ep96992',
            release_date: '',
            badges: null,
            index_title: '33',
            long_title: '第33话 喜欢帅哥的大女儿',
          },
          {
            id: 96993,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '34',
            url: 'https://www.bilibili.com/bangumi/play/ep96993',
            release_date: '',
            badges: null,
            index_title: '34',
            long_title: '第34话 三姐妹的童话故事',
          },
          {
            id: 96994,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '35',
            url: 'https://www.bilibili.com/bangumi/play/ep96994',
            release_date: '',
            badges: null,
            index_title: '35',
            long_title: '第35话 社长不在家',
          },
          {
            id: 96995,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '36',
            url: 'https://www.bilibili.com/bangumi/play/ep96995',
            release_date: '',
            badges: null,
            index_title: '36',
            long_title: '第36话 三姐妹的个人演唱会',
          },
          {
            id: 96996,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '37',
            url: 'https://www.bilibili.com/bangumi/play/ep96996',
            release_date: '',
            badges: null,
            index_title: '37',
            long_title: '第37话 小女儿冒险中的院子',
          },
          {
            id: 96997,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '38',
            url: 'https://www.bilibili.com/bangumi/play/ep96997',
            release_date: '',
            badges: null,
            index_title: '38',
            long_title: '第38话 把房子装饰的漂漂亮亮的吧',
          },
          {
            id: 96998,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '39',
            url: 'https://www.bilibili.com/bangumi/play/ep96998',
            release_date: '',
            badges: null,
            index_title: '39',
            long_title: '第39话 不得了!丢丢',
          },
          {
            id: 96999,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '40',
            url: 'https://www.bilibili.com/bangumi/play/ep96999',
            release_date: '',
            badges: null,
            index_title: '40',
            long_title: '第40话 偶然的对决 社长VS自由人',
          },
          {
            id: 97000,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '41',
            url: 'https://www.bilibili.com/bangumi/play/ep97000',
            release_date: '',
            badges: null,
            index_title: '41',
            long_title: '第41话 三女的语言',
          },
          {
            id: 97001,
            cover:
              'http://i2.hdslb.com/bfs/archive/d03e45e9b431090d9990c830192d4e4d587502b0.jpg',
            title: '42',
            url: 'https://www.bilibili.com/bangumi/play/ep97001',
            release_date: '',
            badges: null,
            index_title: '42',
            long_title: '第42话 没有!!',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/6145abd2091d366f1d592d7ed49129bcb515c310.jpg',
        areas: '日本',
        styles: '萌系',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss4778',
        desc: '原作是由是日本漫画家 松本ぷりっつ 以自己的三个宝贝女儿为蓝本所创作的一部温馨搞笑的漫画。\n故事主要以日常育儿生活中的温馨体验与成长中的趣事为主线。...',
        pubtime: 1207584000,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 0,
          user_count: 0,
        },
        display_info: null,
        pgc_season_id: 4778,
        corner: 2,
        index_show: '全13话',
      },
      {
        type: 'media_bangumi',
        media_id: 3421,
        title: '干物妹！小埋 OVA',
        org_title: '干物妹!うまるちゃん OVA「うまるちゃん もう一回!」',
        media_type: 1,
        cv: '土间埋：田中あいみ\n土间太平：野岛健儿\n海老名菜菜：影山灯\n本场切绘：白石晴香\n橘·希尔芬福特：古川由利奈',
        staff:
          '原作：サンカクヘッド\n脚本：あおしまたかし\n演出：福本洁、太田雅彦\n角色设计：高野绫\n总作画监督：高野绫\n作画监督：中原清隆、松尾真彦、马场一树\n美术监督：池之上由纪\n音乐监督：虾名恭范\n音乐：三泽康广\n动画制作：动画工房',
        season_id: 3421,
        is_avid: false,
        hit_columns: ['title', 'org_title'],
        hit_epids: '',
        season_type: 1,
        season_type_name: '番剧',
        selection_style: 'grid',
        ep_size: 2,
        url: 'https://www.bilibili.com/bangumi/play/ss3421',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 84999,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/974cbaaaf7ecd2935aa101e7f6082c5cc4f2ab64.jpg',
            title: '1',
            url: 'https://www.bilibili.com/bangumi/play/ep84999',
            release_date: '',
            badges: null,
            index_title: '1',
            long_title: '小埋、再一次！',
          },
          {
            id: 104217,
            cover:
              'http://i2.hdslb.com/bfs/archive/baf21ad7cedf355130ec797fb569f90f4353115d.jpg',
            title: '2',
            url: 'https://www.bilibili.com/bangumi/play/ep104217',
            release_date: '',
            badges: null,
            index_title: '2',
            long_title: '小埋的秘密',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/c80bbbb0104e7f60c1df67eebf684917880ab7b2.jpg',
        areas: '日本',
        styles: '治愈/日常/校园/萌系',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss3421',
        desc: '单行本第7、10卷限定版同捆OAD',
        pubtime: 1445184000,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 9.5,
          user_count: 4252,
        },
        display_info: null,
        pgc_season_id: 3421,
        corner: 2,
        index_show: '全2话',
      },
      {
        type: 'media_bangumi',
        media_id: 2956,
        title: '拽妹黛薇儿 第四季',
        org_title: 'Daria Season 4',
        media_type: 1,
        cv: '',
        staff:
          '导演：Karen Disher、Guy Moore、Tony Kluck\n编剧：Glenn Eichler、Peggy Nicoll、Anne D. Bernstein',
        season_id: 2956,
        is_avid: false,
        hit_columns: ['title'],
        hit_epids: '',
        season_type: 1,
        season_type_name: '番剧',
        selection_style: 'grid',
        ep_size: 13,
        url: 'https://www.bilibili.com/bangumi/play/ss2956',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 76588,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '1',
            url: 'https://www.bilibili.com/bangumi/play/ep76588',
            release_date: '',
            badges: null,
            index_title: '1',
            long_title: 'P1',
          },
          {
            id: 76589,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '2',
            url: 'https://www.bilibili.com/bangumi/play/ep76589',
            release_date: '',
            badges: null,
            index_title: '2',
            long_title: 'P2',
          },
          {
            id: 76590,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '3',
            url: 'https://www.bilibili.com/bangumi/play/ep76590',
            release_date: '',
            badges: null,
            index_title: '3',
            long_title: 'P3',
          },
          {
            id: 76591,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '4',
            url: 'https://www.bilibili.com/bangumi/play/ep76591',
            release_date: '',
            badges: null,
            index_title: '4',
            long_title: 'P4',
          },
          {
            id: 76592,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '5',
            url: 'https://www.bilibili.com/bangumi/play/ep76592',
            release_date: '',
            badges: null,
            index_title: '5',
            long_title: 'P5',
          },
          {
            id: 76593,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '6',
            url: 'https://www.bilibili.com/bangumi/play/ep76593',
            release_date: '',
            badges: null,
            index_title: '6',
            long_title: 'P6',
          },
          {
            id: 76594,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '7',
            url: 'https://www.bilibili.com/bangumi/play/ep76594',
            release_date: '',
            badges: null,
            index_title: '7',
            long_title: 'P7',
          },
          {
            id: 76595,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '8',
            url: 'https://www.bilibili.com/bangumi/play/ep76595',
            release_date: '',
            badges: null,
            index_title: '8',
            long_title: 'P8',
          },
          {
            id: 76596,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '9',
            url: 'https://www.bilibili.com/bangumi/play/ep76596',
            release_date: '',
            badges: null,
            index_title: '9',
            long_title: 'P9',
          },
          {
            id: 76597,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '10',
            url: 'https://www.bilibili.com/bangumi/play/ep76597',
            release_date: '',
            badges: null,
            index_title: '10',
            long_title: 'P10',
          },
          {
            id: 76598,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '11',
            url: 'https://www.bilibili.com/bangumi/play/ep76598',
            release_date: '',
            badges: null,
            index_title: '11',
            long_title: 'P11',
          },
          {
            id: 76599,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '12',
            url: 'https://www.bilibili.com/bangumi/play/ep76599',
            release_date: '',
            badges: null,
            index_title: '12',
            long_title: 'P12',
          },
          {
            id: 76600,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/cec36887afa2f196835e3c02dac51f84048b57ac.jpg',
            title: '13',
            url: 'https://www.bilibili.com/bangumi/play/ep76600',
            release_date: '',
            badges: null,
            index_title: '13',
            long_title: 'P13',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/8d34557082bc61e2a1cb43c77909916390807c60.jpg',
        areas: '美国',
        styles: '',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss2956',
        desc: '『Daria，跩妹黛薇儿』是继1993年『Beavis \u0026 Butt-head瘪四与大头蛋』之後MTV力捧的另一部以年轻女孩为主角的卡通动画影集，1997年3月於美国MTV全球首播，不到一年时间Dar...',
        pubtime: 951408000,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 9.6,
          user_count: 163,
        },
        display_info: null,
        pgc_season_id: 2956,
        corner: 2,
        index_show: '全13话',
      },
      {
        type: 'media_bangumi',
        media_id: 2955,
        title: '拽妹黛薇儿 第三季',
        org_title: 'Daria Season 3',
        media_type: 1,
        cv: '',
        staff:
          '导演：Karen Disher\n编剧：Peggy Nicoll、Anne D. Bernstein、Glenn Eichler',
        season_id: 2955,
        is_avid: false,
        hit_columns: ['title'],
        hit_epids: '',
        season_type: 1,
        season_type_name: '番剧',
        selection_style: 'grid',
        ep_size: 13,
        url: 'https://www.bilibili.com/bangumi/play/ss2955',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 76575,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '1',
            url: 'https://www.bilibili.com/bangumi/play/ep76575',
            release_date: '',
            badges: null,
            index_title: '1',
            long_title: 'S3E1',
          },
          {
            id: 76576,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '2',
            url: 'https://www.bilibili.com/bangumi/play/ep76576',
            release_date: '',
            badges: null,
            index_title: '2',
            long_title: 'S3E2',
          },
          {
            id: 76577,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '3',
            url: 'https://www.bilibili.com/bangumi/play/ep76577',
            release_date: '',
            badges: null,
            index_title: '3',
            long_title: 'S3E3',
          },
          {
            id: 76578,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '4',
            url: 'https://www.bilibili.com/bangumi/play/ep76578',
            release_date: '',
            badges: null,
            index_title: '4',
            long_title: 'S3E4',
          },
          {
            id: 76579,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '5',
            url: 'https://www.bilibili.com/bangumi/play/ep76579',
            release_date: '',
            badges: null,
            index_title: '5',
            long_title: 'S3E5',
          },
          {
            id: 76580,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '6',
            url: 'https://www.bilibili.com/bangumi/play/ep76580',
            release_date: '',
            badges: null,
            index_title: '6',
            long_title: 'S3E6',
          },
          {
            id: 76581,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '7',
            url: 'https://www.bilibili.com/bangumi/play/ep76581',
            release_date: '',
            badges: null,
            index_title: '7',
            long_title: 'S3E7',
          },
          {
            id: 76582,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '8',
            url: 'https://www.bilibili.com/bangumi/play/ep76582',
            release_date: '',
            badges: null,
            index_title: '8',
            long_title: 'S3E8',
          },
          {
            id: 76583,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '9',
            url: 'https://www.bilibili.com/bangumi/play/ep76583',
            release_date: '',
            badges: null,
            index_title: '9',
            long_title: 'S3E9',
          },
          {
            id: 76584,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '10',
            url: 'https://www.bilibili.com/bangumi/play/ep76584',
            release_date: '',
            badges: null,
            index_title: '10',
            long_title: 'S3E10',
          },
          {
            id: 76585,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '11',
            url: 'https://www.bilibili.com/bangumi/play/ep76585',
            release_date: '',
            badges: null,
            index_title: '11',
            long_title: 'S3E11',
          },
          {
            id: 76586,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '12',
            url: 'https://www.bilibili.com/bangumi/play/ep76586',
            release_date: '',
            badges: null,
            index_title: '12',
            long_title: 'S3E12',
          },
          {
            id: 76587,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/a0e9098e118d578c8fb2713d866a73b0512490c1.jpg',
            title: '13',
            url: 'https://www.bilibili.com/bangumi/play/ep76587',
            release_date: '',
            badges: null,
            index_title: '13',
            long_title: 'S3E13',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/8d34557082bc61e2a1cb43c77909916390807c60.jpg',
        areas: '美国',
        styles: '',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss2955',
        desc: '『Daria，跩妹黛薇儿』是继1993年『Beavis \u0026 Butt-head瘪四与大头蛋』之後MTV力捧的另一部以年轻女孩为主角的卡通动画影集，1997年3月於美国MTV全球首播，不到一年时间Dar...',
        pubtime: 919180800,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 9.7,
          user_count: 208,
        },
        display_info: null,
        pgc_season_id: 2955,
        corner: 2,
        index_show: '全13话',
      },
      {
        type: 'media_bangumi',
        media_id: 2954,
        title: '拽妹黛薇儿 第二季',
        org_title: 'Daria Season 2',
        media_type: 1,
        cv: '',
        staff: '导演：Karen Disher\n编剧：Glenn Eichler、Susie Lewis',
        season_id: 2954,
        is_avid: false,
        hit_columns: ['title'],
        hit_epids: '',
        season_type: 1,
        season_type_name: '番剧',
        selection_style: 'grid',
        ep_size: 13,
        url: 'https://www.bilibili.com/bangumi/play/ss2954',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 76562,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '1',
            url: 'https://www.bilibili.com/bangumi/play/ep76562',
            release_date: '',
            badges: null,
            index_title: '1',
            long_title: 'P1',
          },
          {
            id: 76563,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '2',
            url: 'https://www.bilibili.com/bangumi/play/ep76563',
            release_date: '',
            badges: null,
            index_title: '2',
            long_title: 'P2',
          },
          {
            id: 76564,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '3',
            url: 'https://www.bilibili.com/bangumi/play/ep76564',
            release_date: '',
            badges: null,
            index_title: '3',
            long_title: 'P3',
          },
          {
            id: 76565,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '6',
            url: 'https://www.bilibili.com/bangumi/play/ep76565',
            release_date: '',
            badges: null,
            index_title: '6',
            long_title: 'P3',
          },
          {
            id: 76566,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '5',
            url: 'https://www.bilibili.com/bangumi/play/ep76566',
            release_date: '',
            badges: null,
            index_title: '5',
            long_title: 'P5',
          },
          {
            id: 76567,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '4',
            url: 'https://www.bilibili.com/bangumi/play/ep76567',
            release_date: '',
            badges: null,
            index_title: '4',
            long_title: 'P4',
          },
          {
            id: 76568,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '7',
            url: 'https://www.bilibili.com/bangumi/play/ep76568',
            release_date: '',
            badges: null,
            index_title: '7',
            long_title: 'P7',
          },
          {
            id: 76569,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '8',
            url: 'https://www.bilibili.com/bangumi/play/ep76569',
            release_date: '',
            badges: null,
            index_title: '8',
            long_title: 'P8',
          },
          {
            id: 76570,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '9',
            url: 'https://www.bilibili.com/bangumi/play/ep76570',
            release_date: '',
            badges: null,
            index_title: '9',
            long_title: 'P9',
          },
          {
            id: 76571,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '10',
            url: 'https://www.bilibili.com/bangumi/play/ep76571',
            release_date: '',
            badges: null,
            index_title: '10',
            long_title: 'P10',
          },
          {
            id: 76572,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '11',
            url: 'https://www.bilibili.com/bangumi/play/ep76572',
            release_date: '',
            badges: null,
            index_title: '11',
            long_title: 'P11',
          },
          {
            id: 76573,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '12',
            url: 'https://www.bilibili.com/bangumi/play/ep76573',
            release_date: '',
            badges: null,
            index_title: '12',
            long_title: 'P12',
          },
          {
            id: 76574,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/4d0d428ee48b9645be474def89e2f75d7e1aefee.jpg',
            title: '13',
            url: 'https://www.bilibili.com/bangumi/play/ep76574',
            release_date: '',
            badges: null,
            index_title: '13',
            long_title: 'P13',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/8d34557082bc61e2a1cb43c77909916390807c60.jpg',
        areas: '美国',
        styles: '',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss2954',
        desc: '『Daria，跩妹黛薇儿』是继1993年『Beavis \u0026 Butt-head瘪四与大头蛋』之後MTV力捧的另一部以年轻女孩为主角的卡通动画影集，1997年3月於美国MTV全球首播，不到一年时间Dar...',
        pubtime: 887558400,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 9.8,
          user_count: 327,
        },
        display_info: null,
        pgc_season_id: 2954,
        corner: 2,
        index_show: '全13话',
      },
      {
        type: 'media_bangumi',
        media_id: 2953,
        title: '拽妹黛薇儿 第一季',
        org_title: 'Daria Season 1',
        media_type: 1,
        cv: '',
        staff:
          '导演：Karen Disher、Guy Moore、Tony Kluck\n编剧：Glenn Eichler、Peggy Nicoll、Anne D. Bernstein',
        season_id: 2953,
        is_avid: false,
        hit_columns: ['title'],
        hit_epids: '',
        season_type: 1,
        season_type_name: '番剧',
        selection_style: 'grid',
        ep_size: 13,
        url: 'https://www.bilibili.com/bangumi/play/ss2953',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 76549,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '1',
            url: 'https://www.bilibili.com/bangumi/play/ep76549',
            release_date: '',
            badges: null,
            index_title: '1',
            long_title: '',
          },
          {
            id: 76550,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '2',
            url: 'https://www.bilibili.com/bangumi/play/ep76550',
            release_date: '',
            badges: null,
            index_title: '2',
            long_title: '',
          },
          {
            id: 76551,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '3',
            url: 'https://www.bilibili.com/bangumi/play/ep76551',
            release_date: '',
            badges: null,
            index_title: '3',
            long_title: '',
          },
          {
            id: 76552,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '4',
            url: 'https://www.bilibili.com/bangumi/play/ep76552',
            release_date: '',
            badges: null,
            index_title: '4',
            long_title: '',
          },
          {
            id: 76553,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '5',
            url: 'https://www.bilibili.com/bangumi/play/ep76553',
            release_date: '',
            badges: null,
            index_title: '5',
            long_title: '',
          },
          {
            id: 76554,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '6',
            url: 'https://www.bilibili.com/bangumi/play/ep76554',
            release_date: '',
            badges: null,
            index_title: '6',
            long_title: '',
          },
          {
            id: 76555,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '7',
            url: 'https://www.bilibili.com/bangumi/play/ep76555',
            release_date: '',
            badges: null,
            index_title: '7',
            long_title: '',
          },
          {
            id: 76556,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '8',
            url: 'https://www.bilibili.com/bangumi/play/ep76556',
            release_date: '',
            badges: null,
            index_title: '8',
            long_title: '',
          },
          {
            id: 76557,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '9',
            url: 'https://www.bilibili.com/bangumi/play/ep76557',
            release_date: '',
            badges: null,
            index_title: '9',
            long_title: '',
          },
          {
            id: 76558,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '10',
            url: 'https://www.bilibili.com/bangumi/play/ep76558',
            release_date: '',
            badges: null,
            index_title: '10',
            long_title: '',
          },
          {
            id: 76559,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '11',
            url: 'https://www.bilibili.com/bangumi/play/ep76559',
            release_date: '',
            badges: null,
            index_title: '11',
            long_title: '',
          },
          {
            id: 76560,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '12',
            url: 'https://www.bilibili.com/bangumi/play/ep76560',
            release_date: '',
            badges: null,
            index_title: '12',
            long_title: '',
          },
          {
            id: 76561,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/49267132272e41f00b9513cfa8b4dc66a2182005.jpg',
            title: '13',
            url: 'https://www.bilibili.com/bangumi/play/ep76561',
            release_date: '',
            badges: null,
            index_title: '13',
            long_title: '',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/8d34557082bc61e2a1cb43c77909916390807c60.jpg',
        areas: '美国',
        styles: '',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss2953',
        desc: '『Daria，跩妹黛薇儿』是继1993年『Beavis \u0026 Butt-head瘪四与大头蛋』之後MTV力捧的另一部以年轻女孩为主角的卡通动画影集，1997年3月於美国MTV全球首播，不到一年时间Dar...',
        pubtime: 857145600,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 9.8,
          user_count: 653,
        },
        display_info: null,
        pgc_season_id: 2953,
        corner: 2,
        index_show: '全13话',
      },
    ],
    show_column: 0,
    in_black_key: 0,
    in_white_key: 0,
  },
}

export const mockBilibiliMediaSearchResponse = {
  code: 0,
  message: '0',
  ttl: 1,
  data: {
    seid: '11744654526748129918',
    page: 1,
    pagesize: 20,
    numResults: 2,
    numPages: 1,
    suggest_keyword: '',
    rqt_type: 'search',
    cost_time: {
      total: '0.016569',
      fetch_lexicon: '0.003004',
      params_check: '0.002094',
      is_risk_query: '0.000128',
      illegal_handler: '0.000012',
      main_handler: '0.009658',
      as_request_format: '0.000222',
      as_request: '0.008935',
      deserialize_response: '0.000094',
      as_response_format: '0.000313',
    },
    exp_list: {
      '5502': true,
      '6605': true,
      '7702': true,
      '9902': true,
      '9924': true,
      '9931': true,
      '9941': true,
      '9961': true,
      '100201': true,
      '100805': true,
      '101505': true,
      '102000': true,
      '102405': true,
      '102801': true,
      '102902': true,
      '103000': true,
      '103103': true,
      '103200': true,
      '103300': true,
      '103400': true,
      '104100': true,
      '104202': true,
      '104400': true,
      '105102': true,
      '105202': true,
      '105400': true,
      '105805': true,
      '106000': true,
      '106201': true,
      '106300': true,
      '106405': true,
      '106503': true,
      '106702': true,
      '106904': true,
      '107001': true,
      '107106': true,
      '107202': true,
      '107403': true,
      '107505': true,
      '107601': true,
      '160002': true,
      '171608': true,
    },
    egg_hit: 0,
    result: [
      {
        type: 'media_ft',
        media_id: 3830,
        title: '逮捕令 剧场版 墨东署最大危机',
        org_title: '逮捕しちゃうぞ the MOVIE',
        media_type: 2,
        cv: '小早川美幸：平松晶子\n辻本夏实：玉川砂記子\n',
        staff:
          '脚本：十川誠志\n导演：西村純二\n人物设定：中嶋敦子\n原作：藤島康介\n色彩设计：もちだたけし\n演出：守岡博\n作画监督：中嶋敦子\n美术监督：吉原俊一郎\n音乐：川井憲次\n',
        season_id: 3830,
        is_avid: false,
        hit_columns: [],
        hit_epids: '',
        season_type: 2,
        season_type_name: '电影',
        selection_style: 'grid',
        ep_size: 1,
        url: 'https://www.bilibili.com/bangumi/play/ss3830?theme=movie',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 86674,
            cover:
              'http://i0.hdslb.com/bfs/bangumi/1713b8b8d8c921128195400fe31eb15e167446b9.jpg',
            title: '1',
            url: 'https://www.bilibili.com/bangumi/play/ep86674?theme=movie',
            release_date: '',
            badges: null,
            index_title: '1',
            long_title: '逮捕令：墨东署最大危机',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/659b30c064e9e419a29fd14293343e97122ece49.png',
        areas: '日本',
        styles: '智斗/时泪/职场',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss3830?theme=movie',
        desc: '美幸和夏实分别完成一年的研修重返墨东署。跟著，区内接连发生多宗案件。\n先有葵双叶和赖子发现一批数量庞大的鎗械，继而发生交通灯神秘故障以及电话线不通的意外。\n后来，眾人在一次捣破走私鎗械交易的行动中发现...',
        pubtime: 924883200,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 9.6,
          user_count: 167,
        },
        display_info: null,
        pgc_season_id: 3830,
        corner: 2,
        index_show: '全1话',
      },
      {
        type: 'media_ft',
        media_id: 38292,
        title: '我在北京·挺好的',
        org_title: '我在北京·挺好的',
        media_type: 5,
        cv: '王茜华、林继东、陶昕然、张明健、关慧卿、王君平、杜宁林、石燕京',
        staff: '导演：姚远\n编剧：刘嘉军',
        season_id: 22719,
        is_avid: false,
        hit_columns: null,
        hit_epids: '',
        season_type: 5,
        season_type_name: '电视剧',
        selection_style: 'grid',
        ep_size: 37,
        url: 'https://www.bilibili.com/bangumi/play/ss22719',
        button_text: '立即观看',
        is_follow: 0,
        is_selection: 1,
        eps: [
          {
            id: 170186,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '1',
            url: 'https://www.bilibili.com/bangumi/play/ep170186',
            release_date: '',
            badges: null,
            index_title: '1',
            long_title: '',
          },
          {
            id: 170187,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '2',
            url: 'https://www.bilibili.com/bangumi/play/ep170187',
            release_date: '',
            badges: null,
            index_title: '2',
            long_title: '',
          },
          {
            id: 170188,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '3',
            url: 'https://www.bilibili.com/bangumi/play/ep170188',
            release_date: '',
            badges: null,
            index_title: '3',
            long_title: '',
          },
          {
            id: 170189,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '4',
            url: 'https://www.bilibili.com/bangumi/play/ep170189',
            release_date: '',
            badges: null,
            index_title: '4',
            long_title: '',
          },
          {
            id: 170190,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '5',
            url: 'https://www.bilibili.com/bangumi/play/ep170190',
            release_date: '',
            badges: null,
            index_title: '5',
            long_title: '',
          },
          {
            id: 170191,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '6',
            url: 'https://www.bilibili.com/bangumi/play/ep170191',
            release_date: '',
            badges: null,
            index_title: '6',
            long_title: '',
          },
          {
            id: 170192,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '7',
            url: 'https://www.bilibili.com/bangumi/play/ep170192',
            release_date: '',
            badges: null,
            index_title: '7',
            long_title: '',
          },
          {
            id: 170193,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '8',
            url: 'https://www.bilibili.com/bangumi/play/ep170193',
            release_date: '',
            badges: null,
            index_title: '8',
            long_title: '',
          },
          {
            id: 170194,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '9',
            url: 'https://www.bilibili.com/bangumi/play/ep170194',
            release_date: '',
            badges: null,
            index_title: '9',
            long_title: '',
          },
          {
            id: 170195,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '10',
            url: 'https://www.bilibili.com/bangumi/play/ep170195',
            release_date: '',
            badges: null,
            index_title: '10',
            long_title: '',
          },
          {
            id: 170196,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '11',
            url: 'https://www.bilibili.com/bangumi/play/ep170196',
            release_date: '',
            badges: null,
            index_title: '11',
            long_title: '',
          },
          {
            id: 170197,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '12',
            url: 'https://www.bilibili.com/bangumi/play/ep170197',
            release_date: '',
            badges: null,
            index_title: '12',
            long_title: '',
          },
          {
            id: 170198,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '13',
            url: 'https://www.bilibili.com/bangumi/play/ep170198',
            release_date: '',
            badges: null,
            index_title: '13',
            long_title: '',
          },
          {
            id: 170199,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '14',
            url: 'https://www.bilibili.com/bangumi/play/ep170199',
            release_date: '',
            badges: null,
            index_title: '14',
            long_title: '',
          },
          {
            id: 170200,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '15',
            url: 'https://www.bilibili.com/bangumi/play/ep170200',
            release_date: '',
            badges: null,
            index_title: '15',
            long_title: '',
          },
          {
            id: 170201,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '16',
            url: 'https://www.bilibili.com/bangumi/play/ep170201',
            release_date: '',
            badges: null,
            index_title: '16',
            long_title: '',
          },
          {
            id: 170202,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '17',
            url: 'https://www.bilibili.com/bangumi/play/ep170202',
            release_date: '',
            badges: null,
            index_title: '17',
            long_title: '',
          },
          {
            id: 170203,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '18',
            url: 'https://www.bilibili.com/bangumi/play/ep170203',
            release_date: '',
            badges: null,
            index_title: '18',
            long_title: '',
          },
          {
            id: 170204,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '19',
            url: 'https://www.bilibili.com/bangumi/play/ep170204',
            release_date: '',
            badges: null,
            index_title: '19',
            long_title: '',
          },
          {
            id: 170205,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '20',
            url: 'https://www.bilibili.com/bangumi/play/ep170205',
            release_date: '',
            badges: null,
            index_title: '20',
            long_title: '',
          },
          {
            id: 170206,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '21',
            url: 'https://www.bilibili.com/bangumi/play/ep170206',
            release_date: '',
            badges: null,
            index_title: '21',
            long_title: '',
          },
          {
            id: 170207,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '22',
            url: 'https://www.bilibili.com/bangumi/play/ep170207',
            release_date: '',
            badges: null,
            index_title: '22',
            long_title: '',
          },
          {
            id: 170208,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '23',
            url: 'https://www.bilibili.com/bangumi/play/ep170208',
            release_date: '',
            badges: null,
            index_title: '23',
            long_title: '',
          },
          {
            id: 170209,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '24',
            url: 'https://www.bilibili.com/bangumi/play/ep170209',
            release_date: '',
            badges: null,
            index_title: '24',
            long_title: '',
          },
          {
            id: 170210,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '25',
            url: 'https://www.bilibili.com/bangumi/play/ep170210',
            release_date: '',
            badges: null,
            index_title: '25',
            long_title: '',
          },
          {
            id: 170211,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '26',
            url: 'https://www.bilibili.com/bangumi/play/ep170211',
            release_date: '',
            badges: null,
            index_title: '26',
            long_title: '',
          },
          {
            id: 170212,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '27',
            url: 'https://www.bilibili.com/bangumi/play/ep170212',
            release_date: '',
            badges: null,
            index_title: '27',
            long_title: '',
          },
          {
            id: 170213,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '28',
            url: 'https://www.bilibili.com/bangumi/play/ep170213',
            release_date: '',
            badges: null,
            index_title: '28',
            long_title: '',
          },
          {
            id: 170214,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '29',
            url: 'https://www.bilibili.com/bangumi/play/ep170214',
            release_date: '',
            badges: null,
            index_title: '29',
            long_title: '',
          },
          {
            id: 170215,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '30',
            url: 'https://www.bilibili.com/bangumi/play/ep170215',
            release_date: '',
            badges: null,
            index_title: '30',
            long_title: '',
          },
          {
            id: 170216,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '31',
            url: 'https://www.bilibili.com/bangumi/play/ep170216',
            release_date: '',
            badges: null,
            index_title: '31',
            long_title: '',
          },
          {
            id: 170217,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '32',
            url: 'https://www.bilibili.com/bangumi/play/ep170217',
            release_date: '',
            badges: null,
            index_title: '32',
            long_title: '',
          },
          {
            id: 170218,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '33',
            url: 'https://www.bilibili.com/bangumi/play/ep170218',
            release_date: '',
            badges: null,
            index_title: '33',
            long_title: '',
          },
          {
            id: 170219,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '34',
            url: 'https://www.bilibili.com/bangumi/play/ep170219',
            release_date: '',
            badges: null,
            index_title: '34',
            long_title: '',
          },
          {
            id: 170220,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '35',
            url: 'https://www.bilibili.com/bangumi/play/ep170220',
            release_date: '',
            badges: null,
            index_title: '35',
            long_title: '',
          },
          {
            id: 170221,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '36',
            url: 'https://www.bilibili.com/bangumi/play/ep170221',
            release_date: '',
            badges: null,
            index_title: '36',
            long_title: '',
          },
          {
            id: 170222,
            cover:
              'http://i1.hdslb.com/bfs/archive/3f14ed9cdadb1fd82176eee3c015ea964f1d4e72.jpg',
            title: '37',
            url: 'https://www.bilibili.com/bangumi/play/ep170222',
            release_date: '',
            badges: null,
            index_title: '37',
            long_title: '',
          },
        ],
        badges: null,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/07684209ce4469d91b7d10f8adab40e5fe21099e.jpg',
        areas: '中国大陆',
        styles: '情感/剧情',
        goto_url: 'https://www.bilibili.com/bangumi/play/ss22719',
        desc: '由西安曲江丫丫影视文化股份有限公司投资制作的女性励志年代剧《我在北京•挺好的》目前正在京城紧锣密鼓地拍摄。该剧讲述了一对成长背景与性格迥异的姐妹三十年的爱恨情仇。剧中，馨艺丫丫的签约演员杜宁林出演两姐...',
        pubtime: 1399392000,
        media_mode: 2,
        fix_pubtime_str: '',
        media_score: {
          score: 0,
          user_count: 0,
        },
        display_info: null,
        pgc_season_id: 22719,
        corner: 2,
        index_show: '更新至第37集',
      },
    ],
    show_column: 0,
    in_black_key: 0,
    in_white_key: 0,
  },
}

export const mockBilibiliBangumiInfoResponse = {
  code: 0,
  message: 'success',
  result: {
    activity: {
      head_bg_url: '',
      id: 0,
      title: '',
    },
    actors:
      '土间埋：田中あいみ\n土间太平：野岛健儿\n海老名菜菜：影山灯\n本场切绘：白石晴香\n橘·希尔芬福特：古川由利奈',
    alias: '',
    areas: [
      {
        id: 2,
        name: '日本',
      },
    ],
    bkg_cover: '',
    cover:
      'http://i0.hdslb.com/bfs/bangumi/c80bbbb0104e7f60c1df67eebf684917880ab7b2.jpg',
    delivery_fragment_video: false,
    enable_vt: false,
    episodes: [
      {
        aid: 3070353,
        badge: '',
        badge_info: {
          bg_color: '#FB7299',
          bg_color_night: '#D44E7D',
          text: '',
        },
        badge_type: 0,
        bvid: 'BV1xs41127Nb',
        cid: 4841926,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/974cbaaaf7ecd2935aa101e7f6082c5cc4f2ab64.jpg',
        dimension: {
          height: 360,
          rotate: 0,
          width: 640,
        },
        duration: 1425000,
        enable_vt: false,
        ep_id: 84999,
        from: 'bangumi',
        id: 84999,
        is_view_hide: false,
        link: 'https://www.bilibili.com/bangumi/play/ep84999',
        long_title: '小埋、再一次！',
        pub_time: 1457693660,
        pv: 0,
        release_date: '',
        rights: {
          allow_dm: 1,
          allow_download: 1,
          area_limit: 0,
        },
        section_type: 0,
        share_copy: '《干物妹！小埋 OVA》第1话 小埋、再一次！',
        share_url: 'https://www.bilibili.com/bangumi/play/ep84999',
        short_link: 'https://b23.tv/ep84999',
        showDrmLoginDialog: false,
        show_title: '第1话 小埋、再一次！',
        skip: {
          ed: {
            end: 1424,
            start: 1334,
          },
          op: {
            end: 90,
            start: 0,
          },
        },
        status: 2,
        subtitle: '已观看2308.6万次',
        title: '1',
        vid: 'vupload_4841926',
      },
      {
        aid: 9942165,
        badge: '',
        badge_info: {
          bg_color: '#FB7299',
          bg_color_night: '#D44E7D',
          text: '',
        },
        badge_type: 0,
        bvid: 'BV1rx411D7k1',
        cid: 16541085,
        cover:
          'http://i2.hdslb.com/bfs/archive/baf21ad7cedf355130ec797fb569f90f4353115d.jpg',
        dimension: {
          height: 360,
          rotate: 0,
          width: 640,
        },
        duration: 1426000,
        enable_vt: false,
        ep_id: 104217,
        from: 'bangumi',
        id: 104217,
        is_view_hide: false,
        link: 'https://www.bilibili.com/bangumi/play/ep104217',
        long_title: '小埋的秘密',
        pub_time: 1492573920,
        pv: 0,
        release_date: '',
        rights: {
          allow_dm: 1,
          allow_download: 1,
          area_limit: 0,
        },
        section_type: 0,
        share_copy: '《干物妹！小埋 OVA》第2话 小埋的秘密',
        share_url: 'https://www.bilibili.com/bangumi/play/ep104217',
        short_link: 'https://b23.tv/ep104217',
        showDrmLoginDialog: false,
        show_title: '第2话 小埋的秘密',
        skip: {
          ed: {
            end: 1425,
            start: 1335,
          },
          op: {
            end: 121,
            start: 0,
          },
        },
        status: 2,
        subtitle: '已观看2308.6万次',
        title: '2',
        vid: '',
      },
    ],
    evaluate: '单行本第7、10卷限定版同捆OAD',
    freya: {
      bubble_show_cnt: 0,
      icon_show: 0,
    },
    hide_ep_vv_vt_dm: 1,
    icon_font: {
      name: 'playdata-square-line@500',
      text: '2308.6万',
    },
    jp_title: '',
    link: 'http://www.bilibili.com/bangumi/media/md3421/',
    media_id: 3421,
    mode: 2,
    new_ep: {
      desc: '已完结, 全2话',
      id: 104217,
      is_new: 0,
      title: '2',
    },
    play_strategy: {
      strategies: [
        'common_section-formal_first_ep',
        'common_section-common_section',
        'common_section-next_season',
        'formal-finish-next_season',
        'formal-end-other_section',
        'formal-end-next_season',
        'ord',
      ],
    },
    positive: {
      id: 20389,
      title: '正片',
    },
    publish: {
      is_finish: 1,
      is_started: 1,
      pub_time: '2015-10-19 00:00:01',
      pub_time_show: '2015年10月19日',
      unknow_pub_date: 0,
      weekday: 0,
    },
    rating: {
      count: 4289,
      score: 9.5,
    },
    record: '',
    rights: {
      allow_bp: 0,
      allow_bp_rank: 0,
      allow_download: 1,
      allow_review: 1,
      area_limit: 316,
      ban_area_show: 1,
      can_watch: 1,
      copyright: 'ugc',
      forbid_pre: 0,
      freya_white: 0,
      is_cover_show: 0,
      is_preview: 0,
      only_vip_download: 0,
      resource: '',
      watch_platform: 0,
    },
    season_id: 3421,
    season_title: '干物妹！小埋 OVA',
    seasons: [
      {
        badge: '大会员',
        badge_info: {
          bg_color: '#FB7299',
          bg_color_night: '#D44E7D',
          text: '大会员',
        },
        badge_type: 0,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/image/e7fb0d62e99b974e00e19b68e51e8b1fcdfc02d5.png',
        enable_vt: false,
        horizontal_cover_1610: '',
        horizontal_cover_169: '',
        icon_font: {
          name: 'playdata-square-line@500',
          text: '2.1亿',
        },
        media_id: 2580,
        new_ep: {
          cover:
            'http://i0.hdslb.com/bfs/bangumi/314261a8eab8618dbb49a4c65fe60942dac0228a.jpg',
          id: 69705,
          index_show: '全12话',
        },
        season_id: 2580,
        season_title: '第一季',
        season_type: 1,
        stat: {
          favorites: 5473564,
          series_follow: 8261690,
          views: 213628661,
          vt: 0,
        },
      },
      {
        badge: '出品',
        badge_info: {
          bg_color: '#00C0FF',
          bg_color_night: '#0B91BE',
          text: '出品',
        },
        badge_type: 1,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/e4edc4b821e92840453fe6e36ade81d92e85d0bc.jpg',
        enable_vt: false,
        horizontal_cover_1610: '',
        horizontal_cover_169: '',
        icon_font: {
          name: 'playdata-square-line@500',
          text: '1.7亿',
        },
        media_id: 6446,
        new_ep: {
          cover:
            'http://i2.hdslb.com/bfs/archive/f9218af7a60a68f513155c019c122b20a52f32b6.jpg',
          id: 115446,
          index_show: '全12话',
        },
        season_id: 6446,
        season_title: '第二季',
        season_type: 1,
        stat: {
          favorites: 5135192,
          series_follow: 8261690,
          views: 168223119,
          vt: 0,
        },
      },
      {
        badge: '独家',
        badge_info: {
          bg_color: '#00C0FF',
          bg_color_night: '#0B91BE',
          text: '独家',
        },
        badge_type: 1,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/image/382b395e09fca6b382d92ba583913f240dec9666.png',
        enable_vt: false,
        horizontal_cover_1610:
          'http://i0.hdslb.com/bfs/bangumi/image/42387f8e7cf34c8bf8a5417cb70222750eb00687.png',
        horizontal_cover_169:
          'http://i0.hdslb.com/bfs/bangumi/image/35046fc8f316042ed0797a0310dbb929627cb822.png',
        icon_font: {
          name: 'playdata-square-line@500',
          text: '2914.7万',
        },
        media_id: 28234567,
        new_ep: {
          cover:
            'http://i0.hdslb.com/bfs/archive/03ce110c8c1f274cfa71a51654635cc4f3445609.png',
          id: 411783,
          index_show: '全12话',
        },
        season_id: 38876,
        season_title: '第一季中配',
        season_type: 1,
        stat: {
          favorites: 351753,
          series_follow: 8261690,
          views: 29147365,
          vt: 0,
        },
      },
      {
        badge: '独家',
        badge_info: {
          bg_color: '#00C0FF',
          bg_color_night: '#0B91BE',
          text: '独家',
        },
        badge_type: 1,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/image/a7052ae15ff4e9d9de932af3c7b89578cd99798c.png',
        enable_vt: false,
        horizontal_cover_1610:
          'http://i0.hdslb.com/bfs/bangumi/image/cde0878511ae28e92a0ed65ee3e7787b1dba31dc.png',
        horizontal_cover_169:
          'http://i0.hdslb.com/bfs/bangumi/image/ee63cc51ae2711473ecaafdccecd7bd84bf48193.png',
        icon_font: {
          name: 'playdata-square-line@500',
          text: '1426.8万',
        },
        media_id: 28234585,
        new_ep: {
          cover:
            'http://i0.hdslb.com/bfs/archive/dcb3603ff3848cf14a497c740484f4cc54cb5e82.png',
          id: 411795,
          index_show: '全12话',
        },
        season_id: 38894,
        season_title: '第二季中配',
        season_type: 1,
        stat: {
          favorites: 154969,
          series_follow: 8261690,
          views: 14268493,
          vt: 0,
        },
      },
      {
        badge: '大会员',
        badge_info: {
          bg_color: '#FB7299',
          bg_color_night: '#D44E7D',
          text: '大会员',
        },
        badge_type: 0,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/image/83bc5ff3e2d5569c4e78a99d2d867ba37c28b40f.jpg',
        enable_vt: false,
        horizontal_cover_1610:
          'http://i0.hdslb.com/bfs/bangumi/image/3ad40bd4c226874054bee032cf56a52df4ef7ea1.png',
        horizontal_cover_169:
          'http://i0.hdslb.com/bfs/bangumi/image/b57f5988c1d2ff1ad685f344df82226f8e7a9b91.png',
        icon_font: {
          name: 'playdata-square-line@500',
          text: '381.2万',
        },
        media_id: 28236459,
        new_ep: {
          cover:
            'http://i0.hdslb.com/bfs/archive/0f99c3bd2fa726224f0e3167d41dcd9cf623ac54.png',
          id: 466113,
          index_show: '全12话',
        },
        season_id: 40753,
        season_title: '第一季粤配',
        season_type: 1,
        stat: {
          favorites: 43068,
          series_follow: 8261690,
          views: 3812330,
          vt: 0,
        },
      },
      {
        badge: '独家',
        badge_info: {
          bg_color: '#00C0FF',
          bg_color_night: '#0B91BE',
          text: '独家',
        },
        badge_type: 1,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/image/c6c1b4679080f9a83f2e43e0163c4d5cf86b9235.jpg',
        enable_vt: false,
        horizontal_cover_1610:
          'http://i0.hdslb.com/bfs/bangumi/image/792e1925c874a72f2cb2cea440a56b251aa93441.jpg',
        horizontal_cover_169:
          'http://i0.hdslb.com/bfs/bangumi/image/861901cffbf881bc985aab9ba109399f5621ecf1.png',
        icon_font: {
          name: 'playdata-square-line@500',
          text: '205.6万',
        },
        media_id: 28236460,
        new_ep: {
          cover:
            'http://i0.hdslb.com/bfs/archive/5aa46c1d4ee91a08a5c0316445361983cdcce6ca.png',
          id: 466289,
          index_show: '全12话',
        },
        season_id: 40754,
        season_title: '第二季粤配',
        season_type: 1,
        stat: {
          favorites: 25309,
          series_follow: 8261690,
          views: 2056379,
          vt: 0,
        },
      },
      {
        badge: '',
        badge_info: {
          bg_color: '#FB7299',
          bg_color_night: '#D44E7D',
          text: '',
        },
        badge_type: 0,
        cover:
          'http://i0.hdslb.com/bfs/bangumi/c80bbbb0104e7f60c1df67eebf684917880ab7b2.jpg',
        enable_vt: false,
        horizontal_cover_1610: '',
        horizontal_cover_169: '',
        icon_font: {
          name: 'playdata-square-line@500',
          text: '2308.6万',
        },
        media_id: 3421,
        new_ep: {
          cover:
            'http://i2.hdslb.com/bfs/archive/baf21ad7cedf355130ec797fb569f90f4353115d.jpg',
          id: 104217,
          index_show: '全2话',
        },
        season_id: 3421,
        season_title: 'OVA',
        season_type: 1,
        stat: {
          favorites: 854117,
          series_follow: 8261690,
          views: 23085593,
          vt: 0,
        },
      },
    ],
    series: {
      display_type: 0,
      series_id: 1760,
      series_title: '干物妹！小埋',
    },
    share_copy: '《干物妹！小埋 OVA》小埋，再临！',
    share_sub_title: '小埋，再临！',
    share_url: 'https://www.bilibili.com/bangumi/play/ss3421',
    show: {
      wide_screen: 0,
    },
    show_season_type: 1,
    square_cover:
      'http://i0.hdslb.com/bfs/bangumi/0291ce1eda5343f5025ba5435e062a0e442c8012.jpg',
    staff:
      '原作：サンカクヘッド\n脚本：あおしまたかし\n演出：福本洁、太田雅彦\n角色设计：高野绫\n总作画监督：高野绫\n作画监督：中原清隆、松尾真彦、马场一树\n美术监督：池之上由纪\n音乐监督：虾名恭范\n音乐：三泽康广\n动画制作：动画工房',
    stat: {
      coins: 76463,
      danmakus: 367410,
      favorite: 7678,
      favorites: 854117,
      follow_text: '826.2万系列追番',
      likes: 61565,
      reply: 17237,
      share: 13861,
      views: 23085593,
      vt: 0,
    },
    status: 2,
    styles: ['治愈', '日常', '校园', '萌系'],
    subtitle: '已观看2308.6万次',
    title: '干物妹！小埋 OVA',
    total: 2,
    type: 1,
    user_status: {
      area_limit: 0,
      ban_area_show: 0,
      follow: 0,
      follow_status: 0,
      login: 1,
      pay: 0,
      pay_pack_paid: 0,
      progress: {
        last_ep_id: 84999,
        last_ep_index: '1',
        last_time: 199,
      },
      sponsor: 0,
      vip_info: {
        due_date: 1750176000000,
        status: 1,
        type: 2,
      },
    },
  },
}

export const mockDanmakuXml = `
<?xml version="1.0" encoding="UTF-8"?><i><chatserver>chat.bilibili.com</chatserver><chatid>194903999</chatid><mission>0</mission><maxlimit>8000</maxlimit><state>0</state><real_name>0</real_name><source>k-v</source><d p="3771.64900,1,25,16777215,1714303992,0,eae98d5c,1570454730638267648,10">分多少----、</d><d p="3524.53000,1,25,16777215,1714303741,0,eae98d5c,1570452622060897280,10">俩人都和不到一起，几十家在一起。</d><d p="1495.85900,1,25,16777215,1714289143,0,eae98d5c,1570330170596846080,9">年轻的姑娘，我们太年轻。</d><d p="1404.59800,1,25,16777215,1665454553,0,da598fdc,1160675937684108288,9">老娘们传老婆舌头</d><d p="488.03000,1,25,16777215,1665453566,0,da598fdc,1160667657297549824,8">我小时候听老一辈管电机还叫电滚子了。。。</d><d p="15.29900,1,25,16777215,1662952754,0,a5c7f222,1139689321327278848,8">22年9月12</d><d p="10.21900,1,25,16777215,1660765633,0,18e4952b,1121342420684976384,7">有，在看</d><d p="3822.52100,4,25,16777215,1657411696,0,c01b5049,1093207559285959168,6">Made in hollywood usa by Metro-Goldwyn-Mayer</d><d p="3043.79400,1,25,9599289,1651982457,0,133e7bca,1047663800301411584,6">打雷在农田里危险</d><d p="172.55700,1,25,16777215,1641959704,0,b148200c,963586855589371904,5">抒情的旋律，温暖人心</d><d p="34.34600,1,25,16777215,1638379533,0,8379763f,58347137923398656,5">还有我呢</d><d p="1814.38800,1,25,16777215,1636941286,0,2033e929,57593082262281728,4">爷军志在跑峰</d><d p="315.71000,1,25,16777215,1617273063,0,44fcccb2,47254543266742279,3">这娶回家，美</d><d p="15.71900,1,25,16777215,1615004527,0,4c169865,46065176818483203,3">我一人？</d><d p="6.30400,1,25,16777215,1610469381,0,7ab637c9,43687454588272643,2">有人吗</d><d p="469.82300,1,25,16777215,1595951258,0,110952ac,36075776534118405,2">这东北味儿贼好听</d><d p="443.91900,1,25,16777215,1595951232,0,110952ac,36075763003293703,1">消灭零弹幕</d></i>
`
