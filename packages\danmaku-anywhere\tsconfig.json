{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist/", "noEmit": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["chrome", "react-dom/canary", "react/canary", "@types/wicg-file-system-access"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@danmaku-anywhere/danmaku-engine": ["../danmaku-engine/src"], "@danmaku-anywhere/danmaku-provider": ["../danmaku-provider/src"], "@danmaku-anywhere/danmaku-converter": ["../danmaku-converter/src"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}, {"path": "../danmaku-engine"}, {"path": "../danmaku-provider"}, {"path": "../danmaku-converter"}]}