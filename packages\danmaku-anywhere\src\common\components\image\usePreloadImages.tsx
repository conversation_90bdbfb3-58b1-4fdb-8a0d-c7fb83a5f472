import Fallback from '@/assets/cover_fallback.webp'
import DanDan<PERSON>lay<PERSON>ogo from '@/assets/dandanplay_logo.webp'
import Apologize from '@/assets/danmaku_apologize.webp'
import Empty from '@/assets/danmaku_empty.webp'
import { useImage } from '@/common/components/image/useImage'

export const images = {
  Fallback,
  DanDanPlayLogo,
  Empty,
  Apologize,
} as const

export const usePreloadImages = () => {
  Object.values(images).forEach((image) => useImage(image))
}
