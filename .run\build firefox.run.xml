<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="build firefox" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/packages/danmaku-anywhere/package.json" />
    <command value="run" />
    <scripts>
      <script value="build" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="VITE_TARGET_BROWSER" value="firefox" />
    </envs>
    <method v="2" />
  </configuration>
</component>