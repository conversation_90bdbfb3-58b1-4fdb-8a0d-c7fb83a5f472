// This file is auto-generated by @hey-api/openapi-ts

export type BangumiListResponse = ResponseBase & {
  /**
   * 番剧列表
   */
  bangumiList?: Array<BangumiIntro> | null
}

export type BangumiIntro = {
  /**
   * 作品编号
   */
  animeId?: number
  /**
   * 作品ID（新）
   */
  bangumiId?: string | null
  /**
   * 作品标题
   */
  animeTitle?: string | null
  /**
   * 海报图片地址
   */
  imageUrl?: string | null
  /**
   * 搜索关键词
   */
  searchKeyword?: string | null
  /**
   * 是否正在连载中
   */
  isOnAir?: boolean
  /**
   * 周几上映，0代表周日，1-6代表周一至周六
   */
  airDay?: number
  /**
   * 当前用户是否已关注（无论是否为已弃番等附加状态）
   */
  isFavorited?: boolean
  /**
   * 是否为限制级别的内容（例如属于R18分级）
   */
  isRestricted?: boolean
  /**
   * 番剧综合评分（综合多个来源的评分求出的加权平均值，0-10分）
   */
  rating?: number
}

export type ResponseBase = {
  /**
   * 错误代码，0表示没有发生错误，非0表示有错误，详细信息会包含在errorMessage属性中
   */
  errorCode?: number
  /**
   * 接口是否调用成功
   */
  success?: boolean
  /**
   * 当发生错误时，说明错误具体原因
   */
  errorMessage?: string | null
}

export type BangumiSeasonListResponse = ResponseBase & {
  /**
   * 番剧季度列表
   */
  seasons?: Array<BangumiSeason> | null
}

export type BangumiSeason = {
  /**
   * 年份
   */
  year?: number
  /**
   * 月份
   */
  month?: number
  /**
   * 季度名称
   */
  seasonName?: string | null
}

export type BangumiQueueIntroResponseV2 = ResponseBase & {
  /**
   * 是否有更多数据可以展示（显示界面上的“更多”按钮）
   */
  hasMore?: boolean
  /**
   * 未看剧集列表
   */
  bangumiList?: Array<BangumiQueueIntroV2> | null
}

export type BangumiQueueIntroV2 = {
  /**
   * 作品编号
   */
  animeId?: number
  /**
   * 作品标题
   */
  animeTitle?: string | null
  /**
   * 最新一集的剧集标题
   */
  episodeTitle?: string | null
  /**
   * 剧集上映日期（无小时分钟，当地时间）
   */
  airDate?: string | null
  /**
   * 海报图片地址
   */
  imageUrl?: string | null
  /**
   * 未看状态的说明，如“今天更新”，“昨天更新”，“有多集未看”等
   */
  description?: string | null
  /**
   * 番剧是否在连载中
   */
  isOnAir?: boolean
}

export type BangumiQueueDetailsResponseV2 = ResponseBase & {
  /**
   * 未看番剧剧集列表
   */
  bangumiList?: Array<BangumiQueueDetailsV2> | null
  /**
   * 已关注但从未看过的番剧列表
   */
  unwatchedBangumiList?: Array<BangumiQueueDetailsV2> | null
}

export type BangumiQueueDetailsV2 = {
  /**
   * 作品编号
   */
  animeId?: number
  /**
   * 作品标题
   */
  animeTitle?: string | null
  /**
   * 是否正在连载中
   */
  isOnAir?: boolean
  /**
   * 海报图片地址
   */
  imageUrl?: string | null
  /**
   * 搜索资源的关键词
   */
  searchKeyword?: string | null
  /**
   * 上次观看时间（null表示尚未看过）
   */
  lastWatched?: string | null
  /**
   * 未看剧集的列表
   */
  episodes?: Array<BangumiQueueEpisodeV2> | null
}

export type BangumiQueueEpisodeV2 = {
  /**
   * 剧集编号（弹幕库编号）
   */
  episodeId?: number
  /**
   * 剧集标题
   */
  episodeTitle?: string | null
  /**
   * 上映日期（无小时分钟，当地时间），可能为null
   */
  airDate?: string | null
}

export type BangumiDetailsResponse = ResponseBase & {
  /**
   * 番剧详情
   */
  bangumi?: BangumiDetails | null
}

export type BangumiDetails = BangumiIntro & {
  /**
   * 作品类型
   */
  type?: AnimeType
  /**
   * 类型描述
   */
  typeDescription?: string | null
  /**
   * 作品标题
   */
  titles?: Array<BangumiTitle> | null
  /**
   * 作品季度列表。可能为空，仅对部分源（如TMDB源）有效
   */
  seasons?: Array<BangumiEpisodeSeason> | null
  /**
   * 剧集列表
   */
  episodes?: Array<BangumiEpisode> | null
  /**
   * 番剧简介
   */
  summary?: string | null
  /**
   * 番剧元数据（名称、制作人员、配音人员等）
   */
  metadata?: Array<string> | null
  /**
   * Bangumi.tv页面地址
   */
  bangumiUrl?: string | null
  /**
   * 用户个人评分（0-10）
   */
  userRating?: number
  /**
   * 关注状态
   */
  favoriteStatus?: FavoriteStatus | null
  /**
   * 用户对此番剧的备注/评论/标签
   */
  comment?: string | null
  /**
   * 各个站点的评分详情
   */
  ratingDetails?: {
    [key: string]: number
  } | null
  /**
   * 与此作品直接关联的其他作品（例如同一作品的不同季、剧场版、OVA等）
   */
  relateds?: Array<BangumiIntro> | null
  /**
   * 与此作品相似的其他作品
   */
  similars?: Array<BangumiIntro> | null
  /**
   * 标签列表
   */
  tags?: Array<BangumiTag> | null
  /**
   * 此作品在其他在线数据库/网站的对应url
   */
  onlineDatabases?: Array<BangumiOnlineDatabase> | null
  /**
   * 预告片列表
   */
  trailers?: Array<BangumiTrailer> | null
}

export type AnimeType =
  | 'tvseries'
  | 'tvspecial'
  | 'ova'
  | 'movie'
  | 'musicvideo'
  | 'web'
  | 'other'
  | 'jpmovie'
  | 'jpdrama'
  | 'unknown'
  | 'tmdbtv'
  | 'tmdbmovie'

export type BangumiTitle = {
  /**
   * 语言
   */
  language?: string | null
  /**
   * 标题
   */
  title?: string | null
}

export type BangumiEpisodeSeason = {
  /**
   * 季度ID
   */
  id?: string | null
  /**
   * 上映日期
   */
  airDate?: string | null
  /**
   * 季度名称
   */
  name?: string | null
  /**
   * 剧集数量
   */
  episodeCount?: number
  /**
   * 季度简介
   */
  summary?: string | null
}

export type BangumiEpisode = {
  /**
   * 季度ID（如果为空表示只有一个季度）
   */
  seasonId?: string | null
  /**
   * 剧集ID（弹幕库编号）
   */
  episodeId?: number
  /**
   * 剧集完整标题
   */
  episodeTitle?: string | null
  /**
   * 剧集短标题（可以用来排序，非纯数字，可能包含字母）
   */
  episodeNumber?: string | null
  /**
   * 上次观看时间（服务器时间，即北京时间）
   */
  lastWatched?: string | null
  /**
   * 本集上映时间（当地时间）
   */
  airDate?: string | null
}

export type FavoriteStatus = 'favorited' | 'finished' | 'abandoned'

export type BangumiTag = {
  /**
   * 标签编号
   */
  id?: number
  /**
   * 标签内容
   */
  name?: string | null
  /**
   * 观众为此标签+1次数
   */
  count?: number
}

export type BangumiOnlineDatabase = {
  /**
   * 网站名称
   */
  name?: string | null
  /**
   * 网址
   */
  url?: string | null
}

export type BangumiTrailer = {
  /**
   * 视频编号
   */
  id?: number
  /**
   * 视频播放页地址
   */
  url?: string | null
  /**
   * 视频标题
   */
  title?: string | null
  /**
   * 视频封面
   */
  imageUrl?: string | null
  /**
   * 发布时间
   */
  date?: string
}

/**
 * 弹幕列表
 */
export type CommentResponseV2 = {
  /**
   * 弹幕数量
   */
  count?: number
  /**
   * 弹幕列表
   */
  comments?: Array<CommentData> | null
}

export type CommentData = {
  /**
   * 弹幕ID
   */
  cid?: number
  /**
   * 弹幕参数（出现时间,模式,颜色,用户ID）
   */
  p?: string | null
  /**
   * 弹幕内容
   */
  m?: string | null
}

export type SendCommentResponseV2 = ResponseBase & {
  /**
   * 此弹幕库中的弹幕ID
   */
  cid?: number
}

export type SendCommentRequest = {
  /**
   * 弹幕出现时间，单位为秒
   */
  time?: number
  /**
   * 弹幕模式：1-普通弹幕，4-顶部弹幕，5-底部弹幕
   */
  mode?: number
  /**
   * 弹幕颜色，计算方式为 Rx255x255+Gx255+B
   */
  color?: number
  /**
   * 弹幕内容，不能长于100个字符
   */
  comment?: string | null
}

export type UserFavoriteResponse = ResponseBase & {
  /**
   * 关注列表
   */
  favorites?: Array<UserFavoriteItem> | null
}

export type UserFavoriteItem = {
  /**
   * 作品编号
   */
  animeId?: number
  /**
   * 作品编号
   */
  bangumiId?: string | null
  /**
   * 作品标题
   */
  animeTitle?: string | null
  /**
   * 作品类型
   */
  type?: AnimeType
  /**
   * 上次关注的时间
   */
  lastFavoriteTime?: string
  /**
   * 上次剧集更新的时间
   */
  lastAirDate?: string | null
  /**
   * 上次播放作品相关剧集的时间
   */
  lastWatchTime?: string | null
  /**
   * 海报图片地址
   */
  imageUrl?: string | null
  /**
   * 此作品的总集数
   */
  episodeTotal?: number
  /**
   * 当前已看的集数
   */
  episodeWatched?: number
  /**
   * 番剧首话上映日期
   */
  startDate?: string | null
  /**
   * 此作品是否正在连载中
   */
  isOnAir?: boolean
  /**
   * 关注状态
   */
  favoriteStatus?: FavoriteStatus
  /**
   * 用户给此作品的评分（1-10分，0代表未评分）
   */
  userRating?: number
  /**
   * 此番剧的综合评分（0-10分）
   */
  rating?: number
}

export type UserAddFavoriteResponse = ResponseBase & {
  [key: string]: never
}

export type UserAddFavoriteRequest = {
  /**
   * 动画作品编号
   */
  animeId?: number
  /**
   * 设定或刷新当前的关注状态。设置为null代表不修改当前状态。
   */
  favoriteStatus?: FavoriteStatus | null
  /**
   * 给作品打分（1-10分），0代表不修改当前分数
   */
  rating?: number
  /**
   * 给作品添加评论，最长为500个字符。当值为null或空字符串时将不修改当前的值。
   * 如果希望清空所有文字，请传入至少一个空格。
   */
  comment?: string | null
}

export type UserDeleteFavoriteResponse = ResponseBase & {
  [key: string]: never
}

export type HomepageResponseV2 = ResponseBase & {
  /**
   * 公告列表
   */
  banners?: Array<BannerPageItem> | null
  /**
   * 未看剧集列表
   */
  bangumiQueueIntroList?: Array<BangumiQueueIntroV2> | null
  /**
   * 新番列表
   */
  shinBangumiList?: Array<BangumiIntro> | null
  /**
   * 动画番剧季度列表
   */
  bangumiSeasons?: Array<BangumiSeason> | null
}

export type BannerPageItem = {
  /**
   * 公告ID
   */
  id?: number
  /**
   * 标题
   */
  title?: string | null
  /**
   * 子标题、描述
   */
  description?: string | null
  /**
   * 落地页链接
   */
  url?: string | null
  /**
   * 图片地址
   */
  imageUrl?: string | null
}

export type BannerResponse = ResponseBase & {
  /**
   * 公告列表
   */
  banners?: Array<BannerPageItem> | null
}

export type LoginResponse = ResponseBase & {
  /**
   * 该用户是否需要先注册弹弹play账号才可正常登录。当此值为true时表示用户使用了QQ微博等第三方登录但没有注册弹弹play账号。
   */
  registerRequired?: boolean
  /**
   * 用户编号
   */
  userId?: number
  /**
   * 弹弹play用户名。如果用户使用第三方账号登录（如QQ微博）且没有关联弹弹play账号，此属性将为null
   */
  userName?: string | null
  /**
   * 旧API中使用的数字形式的token，仅为兼容性设置，不要在新代码中使用此属性
   */
  legacyTokenNumber?: number
  /**
   * 字符串形式的JWT token。将来调用需要验证权限的接口时，需要在HTTP Authorization头中设置“Bearer token”。
   */
  token?: string | null
  /**
   * JWT token过期时间，默认为21天。如果是APP应用开发者账号使用自己的应用登录则为1年。
   */
  tokenExpireTime?: string
  /**
   * 用户注册来源类型
   */
  userType?: string | null
  /**
   * 昵称
   */
  screenName?: string | null
  /**
   * 头像图片的地址
   */
  profileImage?: string | null
  /**
   * 当前登录会话内应用权限列表，可以由此判断能否调用哪些API
   */
  appScope?: string | null
  /**
   * 商品列表
   */
  payConfigs?: Array<PayConfig> | null
  /**
   * 用户权益过期时间（全部为北京时间）
   */
  privileges?: UserPrivileges | null
  /**
   * 消息体验证码
   */
  code?: string | null
  /**
   * 当前时间戳
   */
  ts?: number
}

export type PayConfig = {
  /**
   * 支付渠道（wechat,alipay）
   */
  providerId?: string | null
  /**
   * 支付渠道名称（微信支付，支付宝）
   */
  providerName?: string | null
  /**
   * 商品列表
   */
  items?: Array<PayConfigItem> | null
}

export type PayConfigItem = {
  /**
   * 商品ID
   */
  id?: string | null
  /**
   * 商品名称（如：1个月会员）
   */
  name?: string | null
  /**
   * 商品价格（单位：分）
   */
  price?: number
  /**
   * 货币单位（CNY）
   */
  currency?: string | null
}

/**
 * 用户各类权益到期时间
 */
export type UserPrivileges = {
  /**
   * 会员权益过期时间（北京时间）
   */
  member?: string | null
  /**
   * 弹弹play资源监视器权益过期时间（北京时间）
   */
  resmonitor?: string | null
}

/**
 * 请求用户登录
 */
export type LoginRequest = {
  /**
   * 弹弹play用户名
   */
  userName: string
  /**
   * 用户密码
   */
  password: string
  /**
   * 客户端ID
   */
  appId: string
  /**
   * Unix时间戳：从协调世界时1970年1月1日0时0分0秒起至现在的总秒数，不考虑闰秒。
   */
  unixTimestamp?: number
  /**
   * 通过参数计算得到的32位MD5值，不区分大小写。计算方法请参考接口说明。
   */
  hash: string
}

export type MatchResponseV2 = ResponseBase & {
  /**
   * 是否已精确关联到某个弹幕库
   */
  isMatched?: boolean
  /**
   * 搜索匹配的结果
   */
  matches?: Array<MatchResultV2> | null
}

export type MatchResultV2 = {
  /**
   * 弹幕库ID
   */
  episodeId?: number
  /**
   * 作品ID
   */
  animeId?: number
  /**
   * 作品标题
   */
  animeTitle?: string | null
  /**
   * 剧集标题
   */
  episodeTitle?: string | null
  /**
   * 作品类别
   */
  type?: AnimeType
  /**
   * 类型描述
   */
  typeDescription?: string | null
  /**
   * 弹幕偏移时间（弹幕应延迟多少秒出现）。此数字为负数时表示弹幕应提前多少秒出现。
   */
  shift?: number
}

export type MatchRequest = {
  /**
   * 视频文件名，不包含文件夹名称和扩展名，特殊字符需进行转义。
   */
  fileName?: string | null
  /**
   * 文件前16MB (16x1024x1024 Byte) 数据的32位MD5结果，不区分大小写。
   */
  fileHash?: string | null
  /**
   * 文件总长度，单位为Byte。
   */
  fileSize?: number
  /**
   * [可选]32位整数的视频时长，单位为秒。默认为0。
   */
  videoDuration?: number
  /**
   * [可选]匹配模式。
   */
  matchMode?: MatchMode
}

export type MatchMode = 'hashAndFileName' | 'fileNameOnly' | 'hashOnly'

export type BatchMatchResponse = ResponseBase & {
  /**
   * 批量匹配的结果。将针对每个请求生成对应的结果。
   */
  results?: Array<BatchMatchResponseItem> | null
}

export type BatchMatchResponseItem = {
  success?: boolean
  fileHash?: string | null
  matchResult?: MatchResultV2 | null
}

/**
 * 批量匹配的请求
 */
export type BatchMatchRequest = {
  /**
   * 匹配请求，列表中最多包括32个请求
   */
  requests?: Array<MatchRequest> | null
}

export type UserPlayHistoryResponse = ResponseBase & {
  playHistoryAnimes?: Array<UserPlayHistoryAnime> | null
}

export type UserPlayHistoryAnime = {
  animeId?: number
  animeTitle?: string | null
  /**
   * 作品类别
   */
  type?: AnimeType
  /**
   * 类型描述
   */
  typeDescription?: string | null
  imageUrl?: string | null
  isOnAir?: boolean
  episodes?: Array<BangumiEpisode> | null
}

export type UserAddPlayHistoryResponse = ResponseBase & {
  [key: string]: never
}

export type UserAddPlayHistoryRequest = {
  /**
   * 弹幕库编号列表（最多100项，必须都属于同一作品）
   */
  episodeIdList?: Array<number> | null
  /**
   * 关注此作品（弹幕库编号列表中必须只有一项）
   */
  addToFavorite?: boolean
  /**
   * 给此剧集打分（弹幕库编号列表中必须只有一项）。范围为1-10分，0代表不修改当前评分。
   */
  rating?: number
}

export type RegisterRequestV2 = {
  /**
   * 客户端ID
   */
  appId: string
  /**
   * 用户名。只能包含英文或数字，长度为5-20位，首位不能为数字。
   */
  userName: string
  /**
   * 密码。长度为5到20位之间。
   */
  password: string
  /**
   * 备用邮箱（找回密码用）。长度不能超过50个字符。
   */
  email: string
  /**
   * 昵称。长度不能超过50个字符。
   */
  screenName: string
  /**
   * Unix时间戳：从协调世界时1970年1月1日0时0分0秒起至现在的总秒数，不考虑闰秒。
   */
  unixTimestamp?: number
  /**
   * 通过参数计算得到的32位MD5值，不区分大小写。计算方法请参考接口说明。
   */
  hash: string
}

export type ResetPasswordResponseV2 = ResponseBase & {
  [key: string]: never
}

export type ResetPasswordRequestV2 = {
  /**
   * 应用ID
   */
  appId: string
  /**
   * 用户名
   */
  userName: string
  /**
   * 注册此用户时填写的备用邮箱
   */
  email: string
  /**
   * Unix时间戳：从协调世界时1970年1月1日0时0分0秒起至现在的总秒数，不考虑闰秒。
   */
  unixTimestamp?: number
  /**
   * 通过参数计算得到的32位MD5值，不区分大小写。计算方法请参考接口说明。
   */
  hash: string
}

export type FindMyIdResponse = ResponseBase & {
  [key: string]: never
}

export type FindMyIdRequestV2 = {
  /**
   * 应用ID
   */
  appId: string
  /**
   * 注册此用户时填写的备用邮箱
   */
  email: string
  /**
   * Unix时间戳：从协调世界时1970年1月1日0时0分0秒起至现在的总秒数，不考虑闰秒。
   */
  unixTimestamp?: number
  /**
   * 通过参数计算得到的32位MD5值，不区分大小写。计算方法请参考接口说明。
   */
  hash: string
}

export type RelatedResponseV2 = ResponseBase & {
  /**
   * 关联的第三方弹幕列表
   */
  relateds?: Array<RelatedItemV2> | null
}

export type RelatedItemV2 = {
  /**
   * 第三方弹幕页面地址
   */
  url?: string | null
  /**
   * 弹幕偏移时间。当此值为正数时表示弹幕应延迟多少秒出现，负数时表示弹幕应提前多少秒出现。
   */
  shift?: number
}

export type UserAddRelatedResponse = ResponseBase & {
  [key: string]: never
}

export type UserAddRelatedRequest = {
  /**
   * 弹幕库编号
   */
  episodeId?: number
  /**
   * 关联的第三方网站播放地址
   */
  url?: string | null
  /**
   * 弹幕偏移时间（正数为弹幕延迟几秒出现，负数为弹幕提前几秒出现）
   */
  shift?: number
}

export type UserDeleteRelatedResponse = ResponseBase & {
  [key: string]: never
}

export type SearchAnimeResponse = ResponseBase & {
  /**
   * 作品列表
   */
  animes?: Array<SearchAnimeDetails> | null
}

export type SearchAnimeDetails = {
  /**
   * 作品ID
   */
  animeId?: number
  /**
   * 作品ID（新）
   */
  bangumiId?: string | null
  /**
   * 作品标题
   */
  animeTitle?: string | null
  /**
   * 作品类型
   */
  type?: AnimeType
  /**
   * 类型描述
   */
  typeDescription?: string | null
  /**
   * 海报图片地址
   */
  imageUrl?: string | null
  /**
   * 上映日期
   */
  startDate?: string | null
  /**
   * 剧集总数
   */
  episodeCount?: number
  /**
   * 此作品的综合评分（0-10）
   */
  rating?: number
  /**
   * 当前用户是否已关注此作品
   */
  isFavorited?: boolean
}

export type SearchEpisodesResponse = ResponseBase & {
  /**
   * 是否有更多未显示的搜索结果。当返回的搜索结果过多时此值为`true`
   */
  hasMore?: boolean
  /**
   * 搜索结果（作品信息）列表
   */
  animes?: Array<SearchEpisodesAnime> | null
}

export type SearchEpisodesAnime = {
  /**
   * 作品编号
   */
  animeId?: number
  /**
   * 作品标题
   */
  animeTitle?: string | null
  /**
   * 作品类型
   */
  type?: AnimeType
  /**
   * 类型描述
   */
  typeDescription?: string | null
  /**
   * 此作品的剧集列表
   */
  episodes?: Array<SearchEpisodeDetails> | null
}

export type SearchEpisodeDetails = {
  /**
   * 剧集ID（弹幕库编号）
   */
  episodeId?: number
  /**
   * 剧集标题
   */
  episodeTitle?: string | null
}

export type SearchAdvancedConfigResponse = ResponseBase & {
  /**
   * 类型列表
   */
  types?: Array<ConfigKey> | null
  /**
   * 可用标签列表
   */
  tags?: Array<ConfigKey> | null
  /**
   * 排序依据
   */
  sorts?: Array<ConfigKey> | null
  /**
   * 搜索允许的最早年份
   */
  minYear?: number
  /**
   * 搜索允许的最晚年份
   */
  maxYear?: number
}

export type ConfigKey = {
  /**
   * 搜索中使用的值
   */
  key?: number
  /**
   * 用户界面上显示的文字
   */
  value?: string | null
}

export type SearchBangumiResponse = ResponseBase & {
  /**
   * 搜索结果
   */
  bangumis?: Array<SearchBangumiDetails> | null
}

export type SearchBangumiDetails = SearchAnimeDetails & {
  /**
   * 搜索结果中的排名，用于界面中排序展示，从1开始递增
   */
  rank?: number
  /**
   * 搜索关键词
   */
  searchKeyword?: string | null
  /**
   * 是否正在连载中
   */
  isOnAir?: boolean
  /**
   * 是否为限制级别的内容（例如属于R18分级）
   */
  isRestricted?: boolean
}

export type UserUpdateProfileResponseV2 = ResponseBase & {
  updateScreenName?: string | null
  updateProfileImage?: string | null
}

export type UserUpdatePasswordRequest = {
  /**
   * 旧密码（5-20位）
   */
  oldPassword: string
  /**
   * 新密码（5-20位）
   */
  newPassword: string
}

export type UserUpdateProfileRequest = {
  /**
   * 用户新的昵称（留空将不修改昵称）
   */
  screenName?: string | null
  /**
   * 用户头像图片使用Base64编码后的数据（jpg格式，长度不能超过1MB）。留空将不修改头像图片
   */
  profileImageBase64?: string | null
}

export type UserUpdateEmailRequest = {
  /**
   * 当前的关联邮箱地址
   */
  oldEmail: string
  /**
   * 新的关联邮箱地址
   */
  newEmail: string
}

export type BangumiGetShinBangumiData = {
  body?: never
  path?: never
  query?: {
    /**
     * 是否过滤成人内容
     */
    filterAdultContent?: boolean
  }
  url: '/api/v2/bangumi/shin'
}

export type BangumiGetShinBangumiResponses = {
  200: BangumiListResponse
}

export type BangumiGetShinBangumiResponse =
  BangumiGetShinBangumiResponses[keyof BangumiGetShinBangumiResponses]

export type BangumiGetSeasonBangumiOfAnimeData = {
  body?: never
  path: {
    /**
     * 年份
     */
    year: number
    /**
     * 季度月份（一般指1、4、7、10）
     */
    month: number
  }
  query?: {
    /**
     * 是否过滤成人内容
     */
    filterAdultContent?: boolean
  }
  url: '/api/v2/bangumi/season/anime/{year}/{month}'
}

export type BangumiGetSeasonBangumiOfAnimeResponses = {
  200: BangumiListResponse
}

export type BangumiGetSeasonBangumiOfAnimeResponse =
  BangumiGetSeasonBangumiOfAnimeResponses[keyof BangumiGetSeasonBangumiOfAnimeResponses]

export type BangumiGetSeasonsData = {
  body?: never
  path?: never
  query?: never
  url: '/api/v2/bangumi/season/anime'
}

export type BangumiGetSeasonsResponses = {
  200: BangumiSeasonListResponse
}

export type BangumiGetSeasonsResponse =
  BangumiGetSeasonsResponses[keyof BangumiGetSeasonsResponses]

export type BangumiGetQueueIntroData = {
  body?: never
  path?: never
  query?: never
  url: '/api/v2/bangumi/queue/intro'
}

export type BangumiGetQueueIntroResponses = {
  200: BangumiQueueIntroResponseV2
}

export type BangumiGetQueueIntroResponse =
  BangumiGetQueueIntroResponses[keyof BangumiGetQueueIntroResponses]

export type BangumiGetQueueDetailsData = {
  body?: never
  path?: never
  query?: never
  url: '/api/v2/bangumi/queue/details'
}

export type BangumiGetQueueDetailsResponses = {
  200: BangumiQueueDetailsResponseV2
}

export type BangumiGetQueueDetailsResponse =
  BangumiGetQueueDetailsResponses[keyof BangumiGetQueueDetailsResponses]

export type BangumiGetBangumiDetailsData = {
  body?: never
  path: {
    /**
     * 作品编号
     */
    bangumiId: string
  }
  query?: never
  url: '/api/v2/bangumi/{bangumiId}'
}

export type BangumiGetBangumiDetailsResponses = {
  200: BangumiDetailsResponse
}

export type BangumiGetBangumiDetailsResponse =
  BangumiGetBangumiDetailsResponses[keyof BangumiGetBangumiDetailsResponses]

export type CommentGetCommentData = {
  body?: never
  path: {
    /**
     * 弹幕库编号
     */
    episodeId: number
  }
  query?: {
    /**
     * 起始弹幕编号，忽略此编号以前的弹幕。默认值为0
     */
    from?: number
    /**
     * 是否同时获取关联的第三方弹幕。默认值为false
     */
    withRelated?: boolean
    /**
     * 中文简繁转换。0-不转换，1-转换为简体，2-转换为繁体。
     */
    chConvert?: number
  }
  url: '/api/v2/comment/{episodeId}'
}

export type CommentSendCommentData = {
  /**
   * 弹幕内容
   */
  body: SendCommentRequest
  path: {
    /**
     * 弹幕库ID
     */
    episodeId: number
  }
  query?: never
  url: '/api/v2/comment/{episodeId}'
}

export type CommentSendCommentErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type CommentSendCommentResponses = {
  /**
   * 返回发送弹幕的结果
   */
  200: SendCommentResponseV2
}

export type CommentSendCommentResponse =
  CommentSendCommentResponses[keyof CommentSendCommentResponses]

export type CommentGetExtCommentData = {
  body?: never
  path?: never
  query?: {
    /**
     * 第三方网站的页面地址
     */
    url?: string
    /**
     * 中文简繁转换。0-不转换，1-转换为简体，2-转换为繁体。
     */
    chConvert?: number
  }
  url: '/api/v2/extcomment'
}

export type FavoriteGetUserFavoriteData = {
  body?: never
  path?: never
  query?: {
    /**
     * 只返回正在连载的作品
     */
    onlyOnAir?: boolean
  }
  url: '/api/v2/favorite'
}

export type FavoriteGetUserFavoriteErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type FavoriteGetUserFavoriteResponses = {
  /**
   * 返回用户关注的作品列表
   */
  200: UserFavoriteResponse
}

export type FavoriteGetUserFavoriteResponse =
  FavoriteGetUserFavoriteResponses[keyof FavoriteGetUserFavoriteResponses]

export type FavoriteAddFavoriteData = {
  /**
   * 增加关注作品的信息
   */
  body: UserAddFavoriteRequest
  path?: never
  query?: never
  url: '/api/v2/favorite'
}

export type FavoriteAddFavoriteErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type FavoriteAddFavoriteResponses = {
  /**
   * 返回添加关注的结果
   */
  200: UserAddFavoriteResponse
}

export type FavoriteAddFavoriteResponse =
  FavoriteAddFavoriteResponses[keyof FavoriteAddFavoriteResponses]

export type FavoriteDeleteFavoriteData = {
  body?: never
  path: {
    /**
     * 作品编号
     */
    animeId: number
  }
  query?: never
  url: '/api/v2/favorite/{animeId}'
}

export type FavoriteDeleteFavoriteErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type FavoriteDeleteFavoriteResponses = {
  /**
   * 返回取消关注的结果
   */
  200: UserDeleteFavoriteResponse
}

export type FavoriteDeleteFavoriteResponse =
  FavoriteDeleteFavoriteResponses[keyof FavoriteDeleteFavoriteResponses]

export type HomepageGetHomepageData = {
  body?: never
  path?: never
  query?: {
    /**
     * 是否过滤可能出现的成人内容
     */
    filterAdultContent?: boolean
  }
  url: '/api/v2/homepage'
}

export type HomepageGetHomepageResponses = {
  200: HomepageResponseV2
}

export type HomepageGetHomepageResponse =
  HomepageGetHomepageResponses[keyof HomepageGetHomepageResponses]

export type HomepageGetBannerData = {
  body?: never
  path?: never
  query?: never
  url: '/api/v2/homepage/banner'
}

export type HomepageGetBannerResponses = {
  200: BannerResponse
}

export type HomepageGetBannerResponse =
  HomepageGetBannerResponses[keyof HomepageGetBannerResponses]

export type LoginLoginData = {
  /**
   * 登录请求
   */
  body: LoginRequest
  path?: never
  query?: never
  url: '/api/v2/login'
}

export type LoginLoginResponses = {
  200: LoginResponse
}

export type LoginLoginResponse = LoginLoginResponses[keyof LoginLoginResponses]

export type LoginRenewTokenData = {
  body?: never
  path?: never
  query?: never
  url: '/api/v2/login/renew'
}

export type LoginRenewTokenErrors = {
  /**
   * 未提供有效的Token
   */
  401: unknown
}

export type LoginRenewTokenResponses = {
  /**
   * 返回Token刷新后的用户信息
   */
  200: LoginResponse
}

export type LoginRenewTokenResponse =
  LoginRenewTokenResponses[keyof LoginRenewTokenResponses]

export type MatchMatchData = {
  /**
   * 匹配请求
   */
  body: MatchRequest
  path?: never
  query?: never
  url: '/api/v2/match'
}

export type MatchMatchResponses = {
  200: MatchResponseV2
}

export type MatchMatchResponse = MatchMatchResponses[keyof MatchMatchResponses]

export type MatchBatchMatchData = {
  /**
   * 批量匹配请求
   */
  body: BatchMatchRequest
  path?: never
  query?: never
  url: '/api/v2/match/batch'
}

export type MatchBatchMatchResponses = {
  200: BatchMatchResponse
}

export type MatchBatchMatchResponse =
  MatchBatchMatchResponses[keyof MatchBatchMatchResponses]

export type PlayHistoryGetUserPlayHistoryData = {
  body?: never
  path?: never
  query?: {
    /**
     * 开始日期
     */
    fromDate?: string | null
    /**
     * 结束日期
     */
    toDate?: string | null
  }
  url: '/api/v2/playhistory'
}

export type PlayHistoryGetUserPlayHistoryErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type PlayHistoryGetUserPlayHistoryResponses = {
  /**
   * 返回用户播放历史
   */
  200: UserPlayHistoryResponse
}

export type PlayHistoryGetUserPlayHistoryResponse =
  PlayHistoryGetUserPlayHistoryResponses[keyof PlayHistoryGetUserPlayHistoryResponses]

export type PlayHistoryAddPlayHistoryData = {
  /**
   * 播放历史记录数据
   */
  body: UserAddPlayHistoryRequest
  path?: never
  query?: never
  url: '/api/v2/playhistory'
}

export type PlayHistoryAddPlayHistoryErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type PlayHistoryAddPlayHistoryResponses = {
  /**
   * 返回播放历史添加结果
   */
  200: UserAddPlayHistoryResponse
}

export type PlayHistoryAddPlayHistoryResponse =
  PlayHistoryAddPlayHistoryResponses[keyof PlayHistoryAddPlayHistoryResponses]

export type RegisterRegisterMainUserData = {
  /**
   * 注册请求
   */
  body: RegisterRequestV2
  path?: never
  query?: never
  url: '/api/v2/register'
}

export type RegisterRegisterMainUserResponses = {
  200: LoginResponse
}

export type RegisterRegisterMainUserResponse =
  RegisterRegisterMainUserResponses[keyof RegisterRegisterMainUserResponses]

export type RegisterResetPasswordData = {
  /**
   * 重置密码请求
   */
  body: ResetPasswordRequestV2
  path?: never
  query?: never
  url: '/api/v2/register/resetpassword'
}

export type RegisterResetPasswordResponses = {
  200: ResetPasswordResponseV2
}

export type RegisterResetPasswordResponse =
  RegisterResetPasswordResponses[keyof RegisterResetPasswordResponses]

export type RegisterFindMyIdData = {
  /**
   * 查找用户名请求
   */
  body: FindMyIdRequestV2
  path?: never
  query?: never
  url: '/api/v2/register/findmyid'
}

export type RegisterFindMyIdResponses = {
  200: FindMyIdResponse
}

export type RegisterFindMyIdResponse =
  RegisterFindMyIdResponses[keyof RegisterFindMyIdResponses]

export type RelatedDeleteRelatedData = {
  body?: never
  path: {
    /**
     * 弹幕库编号
     */
    episodeId: number
  }
  query?: {
    /**
     * 第三方弹幕源网站的网址
     */
    url?: string
  }
  url: '/api/v2/related/{episodeId}'
}

export type RelatedDeleteRelatedErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type RelatedDeleteRelatedResponses = {
  /**
   * 返回投票结果
   */
  200: UserDeleteRelatedResponse
}

export type RelatedDeleteRelatedResponse =
  RelatedDeleteRelatedResponses[keyof RelatedDeleteRelatedResponses]

export type RelatedGetRelatedData = {
  body?: never
  path: {
    /**
     * 弹幕库ID
     */
    episodeId: number
  }
  query?: never
  url: '/api/v2/related/{episodeId}'
}

export type RelatedGetRelatedResponses = {
  200: RelatedResponseV2
}

export type RelatedGetRelatedResponse =
  RelatedGetRelatedResponses[keyof RelatedGetRelatedResponses]

export type RelatedAddRelatedData = {
  /**
   * 关联数据
   */
  body: UserAddRelatedRequest
  path: {
    /**
     * 弹幕库编号
     */
    episodeId: number
  }
  query?: never
  url: '/api/v2/related/{episodeId}'
}

export type RelatedAddRelatedErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type RelatedAddRelatedResponses = {
  /**
   * 返回投票结果
   */
  200: UserAddRelatedResponse
}

export type RelatedAddRelatedResponse =
  RelatedAddRelatedResponses[keyof RelatedAddRelatedResponses]

export type SearchSearchAnimeData = {
  body?: never
  path?: never
  query?: {
    keyword?: string
    type?: AnimeType | null
  }
  url: '/api/v2/search/anime'
}

export type SearchSearchAnimeResponses = {
  200: SearchAnimeResponse
}

export type SearchSearchAnimeResponse =
  SearchSearchAnimeResponses[keyof SearchSearchAnimeResponses]

export type SearchSearchTmdbData = {
  body?: never
  path?: never
  query?: {
    keyword?: string
  }
  url: '/api/v2/search/tmdb'
}

export type SearchSearchTmdbResponses = {
  200: SearchAnimeResponse
}

export type SearchSearchTmdbResponse =
  SearchSearchTmdbResponses[keyof SearchSearchTmdbResponses]

export type SearchSearchEpisodesData = {
  body?: never
  path?: never
  query?: {
    /**
     * 作品标题。支持通过中文、日语（含罗马音）、英语搜索，至少为2个字符。
     */
    anime?: string | null
    /**
     * TMDB ID，如果指定此参数，将仅返回此 TMDB ID 的关联作品（可能有多个）。
     */
    tmdbId?: number | null
    /**
     * 剧集标题，默认为空。
     * 当值为纯数字时，将仅保留指定集数的结果。
     * 当值为“movie”时，将仅保留剧场版的结果。
     * 其他情况下，将仅保留剧集标题中包含这个文字的结果，不建议使用。
     */
    episode?: string | null
  }
  url: '/api/v2/search/episodes'
}

export type SearchSearchEpisodesErrors = {
  401: unknown
}

export type SearchSearchEpisodesResponses = {
  200: SearchEpisodesResponse
}

export type SearchSearchEpisodesResponse =
  SearchSearchEpisodesResponses[keyof SearchSearchEpisodesResponses]

export type SearchSearchAnimeByTagData = {
  body?: never
  path?: never
  query?: {
    /**
     * 标签列表。格式请见接口说明。
     */
    tags?: string
  }
  url: '/api/v2/search/tag'
}

export type SearchSearchAnimeByTagResponses = {
  200: SearchAnimeResponse
}

export type SearchSearchAnimeByTagResponse =
  SearchSearchAnimeByTagResponses[keyof SearchSearchAnimeByTagResponses]

export type SearchGetSearchAdvConfigData = {
  body?: never
  path?: never
  query?: {
    source?: string | null
  }
  url: '/api/v2/search/adv/config'
}

export type SearchGetSearchAdvConfigResponses = {
  200: SearchAdvancedConfigResponse
}

export type SearchGetSearchAdvConfigResponse =
  SearchGetSearchAdvConfigResponses[keyof SearchGetSearchAdvConfigResponses]

export type SearchSearchAdvancedData = {
  body?: never
  path?: never
  query?: {
    /**
     * 数据源。anidb|tmdb。默认为anidb
     */
    source?: string | null
    /**
     * 作品标题关键词
     */
    keyword?: string | null
    /**
     * 作品类型
     */
    type?: number | null
    /**
     * 标签，一个或多个数字。若填写多个数字请用英文逗号隔开，例如 12,34,56 。设定多个数字时将搜索同时包含这些标签的作品。
     */
    tags?: string | null
    /**
     * 限定作品上映的年份
     */
    year?: number | null
    /**
     * 限定年份前提下继续限定作品月份
     */
    month?: number | null
    /**
     * 限定最低评分（包含）
     */
    minRate?: number
    /**
     * 限定最高评分（包含）
     */
    maxRate?: number
    /**
     * 只显示限制级别的内容。不提供此参数则不过滤结果，提供true或false都将过滤结果。
     */
    restricted?: boolean | null
    /**
     * 设定排序规则
     */
    sort?: number
  }
  url: '/api/v2/search/adv'
}

export type SearchSearchAdvancedResponses = {
  200: SearchBangumiResponse
}

export type SearchSearchAdvancedResponse =
  SearchSearchAdvancedResponses[keyof SearchSearchAdvancedResponses]

export type UserUpdatePasswordData = {
  /**
   * 修改密码请求
   */
  body: UserUpdatePasswordRequest
  path?: never
  query?: never
  url: '/api/v2/user/password'
}

export type UserUpdatePasswordErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type UserUpdatePasswordResponses = {
  /**
   * 修改密码结果
   */
  200: UserUpdateProfileResponseV2
}

export type UserUpdatePasswordResponse =
  UserUpdatePasswordResponses[keyof UserUpdatePasswordResponses]

export type UserUpdateProfileData = {
  /**
   * 修改用户资料请求
   */
  body: UserUpdateProfileRequest
  path?: never
  query?: never
  url: '/api/v2/user/profile'
}

export type UserUpdateProfileErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type UserUpdateProfileResponses = {
  /**
   * 返回修改结果
   */
  200: UserUpdateProfileResponseV2
}

export type UserUpdateProfileResponse =
  UserUpdateProfileResponses[keyof UserUpdateProfileResponses]

export type UserUpdateUserEmailData = {
  /**
   * 修改关联邮箱请求
   */
  body: UserUpdateEmailRequest
  path?: never
  query?: never
  url: '/api/v2/user/email'
}

export type UserUpdateUserEmailErrors = {
  /**
   * 未登录
   */
  401: unknown
}

export type UserUpdateUserEmailResponses = {
  /**
   * 修改邮箱结果
   */
  200: UserUpdateProfileResponseV2
}

export type UserUpdateUserEmailResponse =
  UserUpdateUserEmailResponses[keyof UserUpdateUserEmailResponses]

export type ClientOptions = {
  baseUrl: 'https://api.dandanplay.net' | (string & {})
}
