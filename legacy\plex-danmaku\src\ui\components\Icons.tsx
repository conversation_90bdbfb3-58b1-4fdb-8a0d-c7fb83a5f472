export const DanmakuIcon = () => {
  return (
    <svg
      viewBox="0 0 400 400"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      // p-id="1713"
      width="32"
      height="24"
    >
      <path
        id="svg_10"
        fill="currentColor"
        d="m228.82,254.62c-10.19,1.42 -19.55,2.67 -28.89,4.04c-11.98,1.75 -23.89,4.18 -35.92,5.21c-8.4,0.72 -15.77,-3.54 -22.31,-8.64c-3.15,-2.46 -2.75,-4.64 1.11,-5.42c5.01,-1.01 10.18,-1.35 15.29,-1.78c21.2,-1.79 42.4,-3.49 63.59,-5.27c2.14,-0.18 4.26,-0.68 6.74,-1.09c0,-12.05 0,-24.09 0,-36.8c-9.94,1.47 -19.61,2.81 -29.23,4.44c-1.13,0.19 -2.09,1.89 -2.97,3.01c-2.74,3.51 -5.13,3.8 -7.33,-0.1c-2.82,-5.01 -5.16,-10.38 -7,-15.84c-5.52,-16.35 -10.5,-32.89 -16.13,-49.21c-1.99,-5.77 -5.26,-11.09 -7.89,-16.64c-2.73,-5.77 -1.28,-8.26 5.08,-7.86c7.93,0.49 15.95,2.84 23.7,2.06c21.29,-2.13 42.5,-5.21 63.66,-8.43c10.14,-1.54 20.99,-2.41 29.93,-6.82c13.96,-6.9 24.73,-1.48 35.99,4.93c5.76,3.28 11.56,6.62 16.83,10.6c4.42,3.34 4.43,7.07 0.13,10.4c-11.04,8.53 -16.25,20.73 -21.88,32.82c-4.27,9.17 -9.05,18.11 -13.81,27.04c-1.23,2.31 -3.02,4.42 -4.91,6.27c-3.7,3.62 -6.48,3.53 -9.49,-0.69c-1.76,-2.47 -3.51,-2.93 -6.24,-2.52c-10.01,1.49 -20.04,2.8 -30.51,4.24l0,36.4c7.45,-0.84 14.82,-1.6 22.16,-2.53c17.46,-2.21 34.88,-4.72 52.37,-6.68c12.18,-1.36 22.99,2.6 32.28,10.48c1.52,1.29 2.29,3.49 3.4,5.26c-1.8,0.98 -3.52,2.52 -5.43,2.82c-4.24,0.69 -8.58,0.96 -12.89,1.04c-16.81,0.32 -33.63,0.2 -50.42,0.82c-13.76,0.5 -27.5,1.82 -41.46,2.79c-1.92,38.38 3.65,77.07 -8.81,114.11c-0.7,0.1 -1.4,0.2 -2.1,0.3c-0.85,-1.76 -2.25,-3.46 -2.47,-5.3c-1.3,-10.87 -2.99,-21.75 -3.31,-32.66c-0.7,-23.46 -0.65,-46.94 -0.9,-70.42c0.03,-1.12 0.04,-2.25 0.04,-4.38zm17.35,-62.21c10.34,-1.56 20.14,-3.35 30.02,-4.41c5.77,-0.62 8.95,-2.71 10.55,-8.64c3.79,-14.06 9,-27.81 9.13,-42.61c0.08,-9.17 -4.37,-14 -13.4,-13.43c-11.22,0.72 -22.38,2.48 -33.54,4.03c-1.1,0.15 -2.76,1.97 -2.8,3.07c-0.26,6.94 -0.13,13.89 -0.13,19.46c8.32,0 16.1,-0.17 23.86,0.07c4.49,0.14 5.93,3.3 2.99,6.68c-1.77,2.04 -4.29,3.8 -6.82,4.74c-6.46,2.4 -13.13,4.27 -19.86,6.4l0,24.64zm-18.77,-63.22c-14.18,2.03 -27.58,3.95 -41.36,5.92c3,21.9 5.91,43.17 8.89,64.94c10.88,-1.47 21.06,-2.77 31.2,-4.31c1.01,-0.15 2.53,-1.77 2.56,-2.74c0.21,-7.46 0.12,-14.92 0.12,-22.31c-9.18,-2.64 -18.91,-1.41 -27.66,-7.13c8.67,-6 18.44,-6.23 27.88,-9.55c-0.53,-8.05 -1.06,-16.21 -1.63,-24.82z"
      />
      <path
        id="svg_11"
        fill="currentColor"
        d="m58.31,306.77c4.37,0.43 8.74,0.9 13.11,1.29c3.97,0.36 7.95,0.56 11.91,0.95c7.81,0.77 12.83,-3 16.65,-9.47c6.85,-11.6 9.36,-24.45 10.75,-37.5c1,-9.4 1.29,-18.94 1.07,-28.4c-0.31,-13.62 -6.68,-18.56 -19.72,-15.2c-12.57,3.25 -24.87,7.38 -34.83,16.42c-3.76,3.41 -6.12,2.71 -8.52,-1.96c-4.56,-8.86 -2.27,-17.1 7.14,-23.13c7.94,-5.08 12.81,-12.04 14.57,-21.12c2.6,-13.39 1.71,-26.45 -4.5,-38.87c-0.82,-1.63 -1.5,-3.34 -2.16,-5.04c-0.35,-0.9 -0.53,-1.87 -1.03,-3.72c2.15,0 3.86,-0.24 5.46,0.06c1.93,0.37 3.93,0.94 5.64,1.88c14.81,8.19 27.91,-0.39 41.46,-4.2c0.79,-0.22 1.47,-1.64 1.79,-2.63c4.36,-13.49 7.24,-27.28 7.69,-41.49c0.22,-7.01 -1.15,-8.22 -8.29,-6.82c-10.42,2.04 -20.78,4.39 -31.12,6.77c-10.92,2.51 -19.22,-0.68 -24.84,-10.52c2.45,-0.4 4.53,-0.94 6.64,-1.06c16.46,-0.94 32.78,-2.86 48.48,-8.11c4.02,-1.34 7.96,-3.47 11.33,-6.03c4.98,-3.79 10.1,-4.43 15.4,-1.77c6.38,3.2 12.58,6.8 18.64,10.57c4.3,2.68 4.39,5.67 0.86,9.42c-0.68,0.73 -1.38,1.49 -2.22,2.01c-16.42,10.18 -21.91,27.39 -28.06,44.07c-0.56,1.51 0.67,4.01 1.65,5.71c4.49,7.7 3.29,10 -5.51,11.32c-12.48,1.87 -24.91,4.02 -37.32,6.29c-1.3,0.24 -3.16,1.99 -3.31,3.22c-1.93,15.95 -3.61,31.94 -5.41,48.5c9.19,-1.16 18.53,-2.27 26.07,-8.18c5.75,-4.51 11.22,-5.33 17.44,-1.86c3.34,1.86 6.69,3.74 9.85,5.88c3.26,2.21 4.19,5.52 1.74,8.63c-6.83,8.66 -7.67,18.94 -8.36,29.23c-0.98,14.6 -1.27,29.26 -2.48,43.84c-1.46,17.49 -5.62,34.24 -18.17,47.48c-3.36,3.54 -7.68,6.3 -11.9,8.86c-3.98,2.42 -8.17,0.97 -9.62,-3.04c-4.8,-13.24 -14.71,-21.45 -26.09,-28.47c-0.98,-0.61 -2,-1.16 -3,-1.74c0.39,-0.7 0.76,-1.38 1.12,-2.07z"
      />
      <path
        id="svg_12"
        fill="currentColor"
        d="m254.44,107.14c2.2,-5.96 4.24,-11.98 6.62,-17.87c4.48,-11.09 9.53,-22.01 8.9,-34.38c-0.14,-2.78 -0.61,-5.59 -1.32,-8.29c-1.16,-4.41 -0.09,-6.88 4.5,-7.33c3.89,-0.37 8.09,-0.28 11.81,0.8c6.45,1.88 12.77,4.39 18.87,7.21c2.9,1.34 4.43,4.11 1.57,7.32c-13.6,15.25 -27.14,30.56 -40.81,45.76c-2.57,2.86 -5.6,5.29 -8.42,7.92c-0.58,-0.38 -1.15,-0.76 -1.72,-1.14z"
      />
      <path
        id="svg_13"
        fill="currentColor"
        d="m227.27,87.26c-0.15,1.32 -0.16,2.66 -0.46,3.94c-2.36,10.27 -8.2,12.4 -16.37,5.77c-12.11,-9.83 -18.57,-23.59 -25.19,-37.15c-1.57,-3.21 0.24,-5.12 3.91,-4.53c14.24,2.3 25.94,9.03 34.27,20.89c2.18,3.11 3.1,7.12 4.59,10.72c-0.26,0.12 -0.5,0.24 -0.75,0.36z"
      />
    </svg>
  )
}

export const SettingsIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      className="bi bi-gear"
      viewBox="0 0 16 16"
    >
      <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z" />
      <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z" />
    </svg>
  )
}

export const InfoIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      className="bi bi-info-circle"
      viewBox="0 0 16 16"
    >
      <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
      <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z" />
    </svg>
  )
}

export const VisibleIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      className="bi bi-eye"
      viewBox="0 0 16 16"
    >
      <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z" />
      <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z" />
    </svg>
  )
}

export const VisibleOffIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      className="bi bi-eye-slash"
      viewBox="0 0 16 16"
    >
      <path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z" />
      <path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z" />
      <path d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12-.708.708z" />
    </svg>
  )
}
