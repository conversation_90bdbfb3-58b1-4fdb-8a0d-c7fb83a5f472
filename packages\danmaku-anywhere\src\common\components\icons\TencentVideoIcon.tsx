export const TencentVideoIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 31 28"
      fill="none"
    >
      <path
        d="M5.28441 21.11C5.97314 25.7548 6.79671 29.2461 12.1675 27.5077C17.3185 25.8481 22.2744 22.8982 26.6267 19.5542C28.1867 18.2161 30.9064 16.3283 30.9603 14.1107C30.9064 11.8848 28.1867 10.0053 26.6267 8.66518C22.2703 5.32111 17.3185 2.37742 12.1675 0.709534C6.79671 -1.02473 5.97314 2.4687 5.28441 7.10932C4.74919 11.7609 4.74919 16.4585 5.28441 21.11Z"
        fill="#009AFF"
      />
      <path
        d="M0.597575 7.97653C1.18673 4.90422 2.07046 2.67414 6.03065 3.43963C5.68858 4.64207 5.43892 5.8689 5.28384 7.10939C4.75069 11.7611 4.75069 16.4584 5.28384 21.1101C5.44013 22.3504 5.68978 23.5771 6.03065 24.7798C2.07253 25.5453 1.1888 23.3153 0.597575 20.2429L0.556082 20.0231L0.477264 19.5791C-0.0205788 15.9499 -0.0205788 12.2696 0.477264 8.64036L0.556082 8.19642L0.597575 7.97653Z"
        fill="#FF8800"
      />
      <path
        d="M6.03122 3.43726C6.30505 3.49119 6.59548 3.55758 6.91495 3.6447C11.7195 4.94541 16.3476 7.246 20.4115 9.85778C21.8637 10.9054 24.4028 12.3741 24.4526 14.1125C24.4028 15.851 21.8616 17.3197 20.4115 18.3652C16.3476 20.977 11.7195 23.2797 6.91495 24.5783C6.62403 24.6622 6.32908 24.7314 6.03122 24.7858C5.68935 23.5826 5.4397 22.3551 5.28441 21.1139C4.74919 16.4624 4.74919 11.7648 5.28441 7.11324C5.43984 5.88012 5.68739 4.66037 6.025 3.46423L6.03122 3.43726Z"
        fill="#43E700"
      />
      <path
        d="M8.8649 9.69633C8.8649 9.69633 8.57031 10.1735 8.57031 14.1191C8.57031 18.0648 8.8649 18.517 8.8649 18.517C8.96447 19.0066 9.15948 19.1622 9.69469 19.0377C9.69469 19.0377 10.5784 18.9008 14.1092 17.1043C17.64 15.3078 18.2291 14.6294 18.2291 14.6294C18.6087 14.2457 18.7104 13.9905 18.2291 13.5922C18.2291 13.5922 17.2583 12.696 14.1092 11.0634C10.9601 9.43079 9.69469 9.17564 9.69469 9.17564C9.26112 9.05117 8.98107 9.19223 8.8649 9.69633Z"
        fill="white"
      />
    </svg>
  )
}
