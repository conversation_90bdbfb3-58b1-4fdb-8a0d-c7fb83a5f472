// ==UserScript==
// @name         Plex Danmaku
// @namespace    https://github.com/Mr-Quin/danmaku-anywhere
// @version      0.0.1
// <AUTHOR>
// @description  Danmaku support for Plex
// @license      MIT
// @source       https://github.com/Mr-Quin/danmaku-anywhere
// @match        http://*:32400/web/index.html*
// @match        https://*:32400/web/index.html*
// @match        https://app.plex.tv/*
// @require      https://cdn.jsdelivr.net/npm/preact@10.13.2/dist/preact.min.js
// ==/UserScript==

(o=>{const e=document.createElement("style");e.dataset.source="vite-plugin-monkey",e.textContent=o,document.head.append(e)})(` ._buttonWarning_1352t_1{position:relative}._buttonWarning_1352t_1:after{content:"";position:absolute;top:2px;right:2px;width:6px;height:6px;background-color:red;border-radius:50%}._toolbar_1352t_16{display:flex;flex-direction:row;justify-content:space-between;margin-left:16px;border:1px solid #fff}._popupPanel_1352t_24{position:absolute;z-index:9999999;max-width:500px}._baseInput_1e3hb_1{height:32px;border-radius:144px;background-color:#262626;color:#ffffffb3;border:0;transition:all .2s}._baseInput_1e3hb_1:focus{background-color:#fff;color:#000}._baseButton_1e3hb_14{padding:4px 8px;border-radius:144px;border:hsla(0,0%,100%,.7) 1px solid;line-height:unset}._baseButton_1e3hb_14:hover{color:#fff}._baseButton_1e3hb_14._disabled_1e3hb_23:hover{color:#ffffff4d}._baseScrollbar_1e3hb_27::-webkit-scrollbar{width:8px}._baseScrollbar_1e3hb_27::-webkit-scrollbar-thumb{background-clip:padding-box;background-color:#fff3;border:3px solid transparent;border-radius:8px;min-height:50px}._disabled_1e3hb_23{color:#ffffff4d;border-color:#ffffff4d}._panel_1e3hb_43{padding:24px;display:flex;flex-direction:column;gap:16px;background-color:#191a1c;border-radius:4px;box-shadow:0 4px 10px #00000059;transform-origin:center top;transition:background .15s ease-out}._panel_1e3hb_43>*{color:#ffffffb3}._panel_1e3hb_43 ._sectionHeader_1e3hb_58{font-size:16px}._chSelection_1e3hb_62{display:flex;justify-content:space-between}._chSelection_1e3hb_62>div{display:flex;flex-direction:column;align-items:center}._sliderInput_1e3hb_72{display:flex;gap:8px;justify-content:space-between}._sliderInput_1e3hb_72>div:first-child{width:60px}._seriesSelection_1e3hb_81{display:flex;gap:8px;flex-wrap:wrap;max-height:200px;overflow-y:auto}._seriesSelectionChip_1e3hb_90{flex-shrink:0;max-width:240px;display:flex;gap:4px;overflow:hidden}._seriesSelectionChip_1e3hb_90>div:nth-child(2){white-space:nowrap;overflow:hidden;text-overflow:ellipsis}._titleSelection_1e3hb_104{display:flex;gap:8px}._titleSelectionInput_1e3hb_109{padding:8px 12px}._episodeSelection_1e3hb_114{width:100%}._episodeSelectionOptions_1e3hb_119{background-color:#202629;color:#fffc;height:24px}._danmakuListWrapper_1e3hb_125{display:flex;flex-direction:column;gap:8px;max-height:200px;overflow-y:auto}._danmakuListWrapper_1e3hb_125 ._danmakuEntry_1e3hb_133{display:flex;flex-direction:row;gap:8px}._danmakuListWrapper_1e3hb_125 ._danmakuEntry_1e3hb_133 ._time_1e3hb_138{width:48px;flex-shrink:0}._danmakuListWrapper_1e3hb_125 ._danmakuEntry_1e3hb_133 ._message_1e3hb_142{flex-grow:1;overflow:hidden;text-overflow:ellipsis}
 `);

(function (preact) {
	'use strict';

	var N,g,ne,Pe,z=0,it=[],K=[],Oe=preact.options.__b,Le=preact.options.__r,Re=preact.options.diffed,ze=preact.options.__c,Be=preact.options.unmount;function O(e,t){preact.options.__h&&preact.options.__h(g,e,z||t),z=0;var n=g.__H||(g.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({__V:K}),n.__[e]}function C(e){return z=1,be(ut,e)}function be(e,t,n){var r=O(N++,2);if(r.t=e,!r.__c&&(r.__=[n?n(t):ut(void 0,t),function(a){var c=r.__N?r.__N[0]:r.__[0],l=r.t(c,a);c!==l&&(r.__N=[l,r.__[1]],r.__c.setState({}));}],r.__c=g,!g.u)){var i=function(a,c,l){if(!r.__c.__H)return !0;var h=r.__c.__H.__.filter(function(p){return p.__c});if(h.every(function(p){return !p.__N}))return !o||o.call(this,a,c,l);var v=!1;return h.forEach(function(p){if(p.__N){var d=p.__[0];p.__=p.__N,p.__N=void 0,d!==p.__[0]&&(v=!0);}}),!(!v&&r.__c.props===a)&&(!o||o.call(this,a,c,l))};g.u=!0;var o=g.shouldComponentUpdate,s=g.componentWillUpdate;g.componentWillUpdate=function(a,c,l){if(this.__e){var h=o;o=void 0,i(a,c,l),o=h;}s&&s.call(this,a,c,l);},g.shouldComponentUpdate=i;}return r.__N||r.__}function w(e,t){var n=O(N++,3);!preact.options.__s&&Se(n.__H,t)&&(n.__=e,n.i=t,g.__H.__h.push(n));}function j(e,t){var n=O(N++,4);!preact.options.__s&&Se(n.__H,t)&&(n.__=e,n.i=t,g.__h.push(n));}function x(e){return z=5,A(function(){return {current:e}},[])}function ot(e,t,n){z=6,j(function(){return typeof e=="function"?(e(t()),function(){return e(null)}):e?(e.current=t(),function(){return e.current=null}):void 0},n==null?n:n.concat(e));}function A(e,t){var n=O(N++,7);return Se(n.__H,t)?(n.__V=e(),n.i=t,n.__h=e,n.__V):n.__}function st(e,t){return z=8,A(function(){return e},t)}function at(e){var t=g.context[e.__c],n=O(N++,9);return n.c=e,t?(n.__==null&&(n.__=!0,t.sub(g)),t.props.value):e.__}function we(e,t){preact.options.useDebugValue&&preact.options.useDebugValue(t?t(e):e);}function rn(e){var t=O(N++,10),n=C();return t.__=e,g.componentDidCatch||(g.componentDidCatch=function(r,i){t.__&&t.__(r,i),n[1](r);}),[n[0],function(){n[1](void 0);}]}function ct(){var e=O(N++,11);if(!e.__){for(var t=g.__v;t!==null&&!t.__m&&t.__!==null;)t=t.__;var n=t.__m||(t.__m=[0,0]);e.__="P"+n[0]+"-"+n[1]++;}return e.__}function on(){for(var e;e=it.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(G),e.__H.__h.forEach(le),e.__H.__h=[];}catch(t){e.__H.__h=[],preact.options.__e(t,e.__v);}}preact.options.__b=function(e){g=null,Oe&&Oe(e);},preact.options.__r=function(e){Le&&Le(e),N=0;var t=(g=e.__c).__H;t&&(ne===g?(t.__h=[],g.__h=[],t.__.forEach(function(n){n.__N&&(n.__=n.__N),n.__V=K,n.__N=n.i=void 0;})):(t.__h.forEach(G),t.__h.forEach(le),t.__h=[])),ne=g;},preact.options.diffed=function(e){Re&&Re(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(it.push(t)!==1&&Pe===preact.options.requestAnimationFrame||((Pe=preact.options.requestAnimationFrame)||sn)(on)),t.__H.__.forEach(function(n){n.i&&(n.__H=n.i),n.__V!==K&&(n.__=n.__V),n.i=void 0,n.__V=K;})),ne=g=null;},preact.options.__c=function(e,t){t.some(function(n){try{n.__h.forEach(G),n.__h=n.__h.filter(function(r){return !r.__||le(r)});}catch(r){t.some(function(i){i.__h&&(i.__h=[]);}),t=[],preact.options.__e(r,n.__v);}}),ze&&ze(e,t);},preact.options.unmount=function(e){Be&&Be(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(r){try{G(r);}catch(i){t=i;}}),n.__H=void 0,t&&preact.options.__e(t,n.__v));};var Ae=typeof requestAnimationFrame=="function";function sn(e){var t,n=function(){clearTimeout(r),Ae&&cancelAnimationFrame(t),setTimeout(e);},r=setTimeout(n,100);Ae&&(t=requestAnimationFrame(n));}function G(e){var t=g,n=e.__c;typeof n=="function"&&(e.__c=void 0,n()),g=t;}function le(e){var t=g;e.__c=e.__(),g=t;}function Se(e,t){return !e||e.length!==t.length||t.some(function(n,r){return n!==e[r]})}function ut(e,t){return typeof t=="function"?t(e):t}function lt(e,t){for(var n in t)e[n]=t[n];return e}function de(e,t){for(var n in e)if(n!=="__source"&&!(n in t))return !0;for(var r in t)if(r!=="__source"&&e[r]!==t[r])return !0;return !1}function re(e,t){return e===t&&(e!==0||1/e==1/t)||e!=e&&t!=t}function J(e){this.props=e;}function dt(e,t){function n(i){var o=this.props.ref,s=o==i.ref;return !s&&o&&(o.call?o(null):o.current=null),t?!t(this.props,i)||!s:de(this.props,i)}function r(i){return this.shouldComponentUpdate=n,preact.createElement(e,i)}return r.displayName="Memo("+(e.displayName||e.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r}(J.prototype=new preact.Component).isPureReactComponent=!0,J.prototype.shouldComponentUpdate=function(e,t){return de(this.props,e)||de(this.state,t)};var Fe=preact.options.__b;preact.options.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),Fe&&Fe(e);};var an=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function F(e){function t(n){var r=lt({},n);return delete r.ref,e(r,n.ref||null)}return t.$$typeof=an,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var Ve=function(e,t){return e==null?null:preact.toChildArray(preact.toChildArray(e).map(t))},ft={map:Ve,forEach:Ve,count:function(e){return e?preact.toChildArray(e).length:0},only:function(e){var t=preact.toChildArray(e);if(t.length!==1)throw "Children.only";return t[0]},toArray:preact.toChildArray},cn=preact.options.__e;preact.options.__e=function(e,t,n,r){if(e.then){for(var i,o=t;o=o.__;)if((i=o.__c)&&i.__c)return t.__e==null&&(t.__e=n.__e,t.__k=n.__k),i.__c(e,t)}cn(e,t,n,r);};var He=preact.options.unmount;function ht(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach(function(r){typeof r.__c=="function"&&r.__c();}),e.__c.__H=null),(e=lt({},e)).__c!=null&&(e.__c.__P===n&&(e.__c.__P=t),e.__c=null),e.__k=e.__k&&e.__k.map(function(r){return ht(r,t,n)})),e}function _t(e,t,n){return e&&(e.__v=null,e.__k=e.__k&&e.__k.map(function(r){return _t(r,t,n)}),e.__c&&e.__c.__P===t&&(e.__e&&n.insertBefore(e.__e,e.__d),e.__c.__e=!0,e.__c.__P=n)),e}function H(){this.__u=0,this.t=null,this.__b=null;}function pt(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function mt(e){var t,n,r;function i(o){if(t||(t=e()).then(function(s){n=s.default||s;},function(s){r=s;}),r)throw r;if(!n)throw t;return preact.createElement(n,o)}return i.displayName="Lazy",i.__f=!0,i}function L(){this.u=null,this.o=null;}preact.options.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&e.__h===!0&&(e.type=null),He&&He(e);},(H.prototype=new preact.Component).__c=function(e,t){var n=t.__c,r=this;r.t==null&&(r.t=[]),r.t.push(n);var i=pt(r.__v),o=!1,s=function(){o||(o=!0,n.__R=null,i?i(a):a());};n.__R=s;var a=function(){if(!--r.__u){if(r.state.__a){var l=r.state.__a;r.__v.__k[0]=_t(l,l.__c.__P,l.__c.__O);}var h;for(r.setState({__a:r.__b=null});h=r.t.pop();)h.forceUpdate();}},c=t.__h===!0;r.__u++||c||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(s,s);},H.prototype.componentWillUnmount=function(){this.t=[];},H.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=ht(this.__b,n,r.__O=r.__P);}this.__b=null;}var i=t.__a&&preact.createElement(preact.Fragment,null,e.fallback);return i&&(i.__h=null),[preact.createElement(preact.Fragment,null,t.__a?null:e.children),i]};var We=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&(e.props.revealOrder[0]!=="t"||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2];}};function un(e){return this.getChildContext=function(){return e.context},e.children}function ln(e){var t=this,n=e.i;t.componentWillUnmount=function(){preact.render(null,t.l),t.l=null,t.i=null;},t.i&&t.i!==n&&t.componentWillUnmount(),e.__v?(t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(r){this.childNodes.push(r),t.i.appendChild(r);},insertBefore:function(r,i){this.childNodes.push(r),t.i.appendChild(r);},removeChild:function(r){this.childNodes.splice(this.childNodes.indexOf(r)>>>1,1),t.i.removeChild(r);}}),preact.render(preact.createElement(un,{context:t.context},e.__v),t.l)):t.l&&t.componentWillUnmount();}function Q(e,t){var n=preact.createElement(ln,{__v:e,i:t});return n.containerInfo=t,n}(L.prototype=new preact.Component).__a=function(e){var t=this,n=pt(t.__v),r=t.o.get(e);return r[0]++,function(i){var o=function(){t.props.revealOrder?(r.push(i),We(t,e,r)):i();};n?n(o):o();}},L.prototype.render=function(e){this.u=null,this.o=new Map;var t=preact.toChildArray(e.children);e.revealOrder&&e.revealOrder[0]==="b"&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},L.prototype.componentDidUpdate=L.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(t,n){We(e,n,t);});};var vt=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,dn=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,fn=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,hn=/[A-Z0-9]/g,_n=typeof document<"u",pn=function(e){return (typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(e)};function gt(e,t,n){return t.__k==null&&(t.textContent=""),preact.render(e,t),typeof n=="function"&&n(),e?e.__c:null}function yt(e,t,n){return preact.hydrate(e,t),typeof n=="function"&&n(),e?e.__c:null}preact.Component.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(preact.Component.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t});}});});var je=preact.options.event;function mn(){}function vn(){return this.cancelBubble}function gn(){return this.defaultPrevented}preact.options.event=function(e){return je&&(e=je(e)),e.persist=mn,e.isPropagationStopped=vn,e.isDefaultPrevented=gn,e.nativeEvent=e};var ke,yn={enumerable:!1,configurable:!0,get:function(){return this.class}},Ue=preact.options.vnode;preact.options.vnode=function(e){typeof e.type=="string"&&function(t){var n=t.props,r=t.type,i={};for(var o in n){var s=n[o];if(!(o==="value"&&"defaultValue"in n&&s==null||_n&&o==="children"&&r==="noscript"||o==="class"||o==="className")){var a=o.toLowerCase();o==="defaultValue"&&"value"in n&&n.value==null?o="value":o==="download"&&s===!0?s="":a==="ondoubleclick"?o="ondblclick":a!=="onchange"||r!=="input"&&r!=="textarea"||pn(n.type)?a==="onfocus"?o="onfocusin":a==="onblur"?o="onfocusout":fn.test(o)?o=a:r.indexOf("-")===-1&&dn.test(o)?o=o.replace(hn,"-$&").toLowerCase():s===null&&(s=void 0):a=o="oninput",a==="oninput"&&i[o=a]&&(o="oninputCapture"),i[o]=s;}}r=="select"&&i.multiple&&Array.isArray(i.value)&&(i.value=preact.toChildArray(n.children).forEach(function(c){c.props.selected=i.value.indexOf(c.props.value)!=-1;})),r=="select"&&i.defaultValue!=null&&(i.value=preact.toChildArray(n.children).forEach(function(c){c.props.selected=i.multiple?i.defaultValue.indexOf(c.props.value)!=-1:i.defaultValue==c.props.value;})),n.class&&!n.className?(i.class=n.class,Object.defineProperty(i,"className",yn)):(n.className&&!n.class||n.class&&n.className)&&(i.class=i.className=n.className),t.props=i;}(e),e.$$typeof=vt,Ue&&Ue(e);};var qe=preact.options.__r;preact.options.__r=function(e){qe&&qe(e),ke=e.__c;};var Ke=preact.options.diffed;preact.options.diffed=function(e){Ke&&Ke(e);var t=e.props,n=e.__e;n!=null&&e.type==="textarea"&&"value"in t&&t.value!==n.value&&(n.value=t.value==null?"":t.value),ke=null;};var bt={ReactCurrentDispatcher:{current:{readContext:function(e){return ke.__n[e.__c].props.value}}}},bn="17.0.2";function wt(e){return preact.createElement.bind(null,e)}function Ce(e){return !!e&&e.$$typeof===vt}function St(e){return Ce(e)?preact.cloneElement.apply(null,arguments):e}function kt(e){return !!e.__k&&(preact.render(null,e),!0)}function Ct(e){return e&&(e.base||e.nodeType===1&&e)||null}var xt=function(e,t){return e(t)},Et=function(e,t){return e(t)},Mt=preact.Fragment;function xe(e){e();}function Dt(e){return e}function It(){return [!1,xe]}var Tt=j;function Nt(e,t){var n=t(),r=C({h:{__:n,v:t}}),i=r[0].h,o=r[1];return j(function(){i.__=n,i.v=t,re(i.__,t())||o({h:i});},[e,n,t]),w(function(){return re(i.__,i.v())||o({h:i}),e(function(){re(i.__,i.v())||o({h:i});})},[e]),n}var wn={useState:C,useId:ct,useReducer:be,useEffect:w,useLayoutEffect:j,useInsertionEffect:Tt,useTransition:It,useDeferredValue:Dt,useSyncExternalStore:Nt,startTransition:xe,useRef:x,useImperativeHandle:ot,useMemo:A,useCallback:st,useContext:at,useDebugValue:we,version:"17.0.2",Children:ft,render:gt,hydrate:yt,unmountComponentAtNode:kt,createPortal:Q,createElement:preact.createElement,createContext:preact.createContext,createFactory:wt,cloneElement:St,createRef:preact.createRef,Fragment:preact.Fragment,isValidElement:Ce,findDOMNode:Ct,Component:preact.Component,PureComponent:J,memo:dt,forwardRef:F,flushSync:Et,unstable_batchedUpdates:xt,StrictMode:Mt,Suspense:H,SuspenseList:L,lazy:mt,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:bt};const Sn=Object.freeze(Object.defineProperty({__proto__:null,Children:ft,Component:preact.Component,Fragment:preact.Fragment,PureComponent:J,StrictMode:Mt,Suspense:H,SuspenseList:L,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:bt,cloneElement:St,createContext:preact.createContext,createElement:preact.createElement,createFactory:wt,createPortal:Q,createRef:preact.createRef,default:wn,findDOMNode:Ct,flushSync:Et,forwardRef:F,hydrate:yt,isValidElement:Ce,lazy:mt,memo:dt,render:gt,startTransition:xe,unmountComponentAtNode:kt,unstable_batchedUpdates:xt,useCallback:st,useContext:at,useDebugValue:we,useDeferredValue:Dt,useEffect:w,useErrorBoundary:rn,useId:ct,useImperativeHandle:ot,useInsertionEffect:Tt,useLayoutEffect:j,useMemo:A,useReducer:be,useRef:x,useState:C,useSyncExternalStore:Nt,useTransition:It,version:bn},Symbol.toStringTag,{value:"Module"})),kn="[Danmaku]",f=new Proxy(console,{get(e,t){return typeof e[t]=="function"?(...n)=>{e[t](kn,...n);}:e[t]}}),Cn="Player-fullPlayerContainer-wBDz23",xn=()=>{const[e,t]=C(null),[n,r]=C(null),[i,o]=C(!0),[s,a]=C(null),c=x(null);return w(()=>{const l=new MutationObserver(h=>{for(const v of h){for(const d of v.addedNodes)if(d instanceof HTMLElement&&d.nodeName==="VIDEO"){f.debug("Plex video player found"),r(d),c.current=d;const y=d.parentNode.parentNode;t(y),a(y);return}const p=Array.from(v.removedNodes);(p.includes(c.current)||p.some(d=>d.contains(c.current)))&&(f.debug("Plex video player removed"),a(null),r(null),c.current=null,t(null));}});return l.observe(document.body,{childList:!0,subtree:!0}),()=>{l.disconnect();}},[]),w(()=>{if(!s)return;const l=new MutationObserver(h=>{for(const v of h)if(v.type==="attributes"){const d=v.target.classList.contains(Cn);f.debug(`Plex video player changed to ${d?"fullscreen":"minimized"}`),o(d);return}});return l.observe(s,{attributes:!0,attributeFilter:["class"]}),()=>{l.disconnect();}},[s]),[e,n,i]},En=(e,t)=>t.some(n=>e instanceof n);let Ge,Ye;function Mn(){return Ge||(Ge=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function Dn(){return Ye||(Ye=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}const $t=new WeakMap,fe=new WeakMap,Pt=new WeakMap,ie=new WeakMap,Ee=new WeakMap;function In(e){const t=new Promise((n,r)=>{const i=()=>{e.removeEventListener("success",o),e.removeEventListener("error",s);},o=()=>{n(T(e.result)),i();},s=()=>{r(e.error),i();};e.addEventListener("success",o),e.addEventListener("error",s);});return t.then(n=>{n instanceof IDBCursor&&$t.set(n,e);}).catch(()=>{}),Ee.set(t,e),t}function Tn(e){if(fe.has(e))return;const t=new Promise((n,r)=>{const i=()=>{e.removeEventListener("complete",o),e.removeEventListener("error",s),e.removeEventListener("abort",s);},o=()=>{n(),i();},s=()=>{r(e.error||new DOMException("AbortError","AbortError")),i();};e.addEventListener("complete",o),e.addEventListener("error",s),e.addEventListener("abort",s);});fe.set(e,t);}let he={get(e,t,n){if(e instanceof IDBTransaction){if(t==="done")return fe.get(e);if(t==="objectStoreNames")return e.objectStoreNames||Pt.get(e);if(t==="store")return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return T(e[t])},set(e,t,n){return e[t]=n,!0},has(e,t){return e instanceof IDBTransaction&&(t==="done"||t==="store")?!0:t in e}};function Nn(e){he=e(he);}function $n(e){return e===IDBDatabase.prototype.transaction&&!("objectStoreNames"in IDBTransaction.prototype)?function(t,...n){const r=e.call(oe(this),t,...n);return Pt.set(r,t.sort?t.sort():[t]),T(r)}:Dn().includes(e)?function(...t){return e.apply(oe(this),t),T($t.get(this))}:function(...t){return T(e.apply(oe(this),t))}}function Pn(e){return typeof e=="function"?$n(e):(e instanceof IDBTransaction&&Tn(e),En(e,Mn())?new Proxy(e,he):e)}function T(e){if(e instanceof IDBRequest)return In(e);if(ie.has(e))return ie.get(e);const t=Pn(e);return t!==e&&(ie.set(e,t),Ee.set(t,e)),t}const oe=e=>Ee.get(e);function On(e,t,{blocked:n,upgrade:r,blocking:i,terminated:o}={}){const s=indexedDB.open(e,t),a=T(s);return r&&s.addEventListener("upgradeneeded",c=>{r(T(s.result),c.oldVersion,c.newVersion,T(s.transaction),c);}),n&&s.addEventListener("blocked",c=>n(c.oldVersion,c.newVersion,c)),a.then(c=>{o&&c.addEventListener("close",()=>o()),i&&c.addEventListener("versionchange",l=>i(l.oldVersion,l.newVersion,l));}).catch(()=>{}),a}const Ln=["get","getKey","getAll","getAllKeys","count"],Rn=["put","add","delete","clear"],se=new Map;function Je(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&typeof t=="string"))return;if(se.get(t))return se.get(t);const n=t.replace(/FromIndex$/,""),r=t!==n,i=Rn.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!(i||Ln.includes(n)))return;const o=async function(s,...a){const c=this.transaction(s,i?"readwrite":"readonly");let l=c.store;return r&&(l=l.index(a.shift())),(await Promise.all([l[n](...a),i&&c.done]))[0]};return se.set(t,o),o}Nn(e=>({...e,get:(t,n,r)=>Je(t,n)||e.get(t,n,r),has:(t,n)=>!!Je(t,n)||e.has(t,n)}));const Ze=e=>{let t;const n=new Set,r=(c,l)=>{const h=typeof c=="function"?c(t):c;if(!Object.is(h,t)){const v=t;t=l??typeof h!="object"?h:Object.assign({},t,h),n.forEach(p=>p(t,v));}},i=()=>t,a={setState:r,getState:i,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{(({BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1})&&"production")!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear();}};return t=e(r,i,a),a},zn=e=>e?Ze(e):Ze;function Bn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function An(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){if(this instanceof r){var i=[null];i.push.apply(i,arguments);var o=Function.bind.apply(t,i);return new o}return t.apply(this,arguments)};n.prototype=t.prototype;}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return e[r]}});}),n}var _e={},Fn={get exports(){return _e},set exports(e){_e=e;}},Ot={};const Lt=An(Sn);var pe={},Vn={get exports(){return pe},set exports(e){pe=e;}},Rt={};/**
	 * @license React
	 * use-sync-external-store-shim.production.min.js
	 *
	 * Copyright (c) Facebook, Inc. and its affiliates.
	 *
	 * This source code is licensed under the MIT license found in the
	 * LICENSE file in the root directory of this source tree.
	 */var B=Lt;function Hn(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Wn=typeof Object.is=="function"?Object.is:Hn,jn=B.useState,Un=B.useEffect,qn=B.useLayoutEffect,Kn=B.useDebugValue;function Gn(e,t){var n=t(),r=jn({inst:{value:n,getSnapshot:t}}),i=r[0].inst,o=r[1];return qn(function(){i.value=n,i.getSnapshot=t,ae(i)&&o({inst:i});},[e,n,t]),Un(function(){return ae(i)&&o({inst:i}),e(function(){ae(i)&&o({inst:i});})},[e]),Kn(n),n}function ae(e){var t=e.getSnapshot;e=e.value;try{var n=t();return !Wn(e,n)}catch{return !0}}function Yn(e,t){return t()}var Jn=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Yn:Gn;Rt.useSyncExternalStore=B.useSyncExternalStore!==void 0?B.useSyncExternalStore:Jn;(function(e){e.exports=Rt;})(Vn);/**
	 * @license React
	 * use-sync-external-store-shim/with-selector.production.min.js
	 *
	 * Copyright (c) Facebook, Inc. and its affiliates.
	 *
	 * This source code is licensed under the MIT license found in the
	 * LICENSE file in the root directory of this source tree.
	 */var ee=Lt,Zn=pe;function Xn(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qn=typeof Object.is=="function"?Object.is:Xn,er=Zn.useSyncExternalStore,tr=ee.useRef,nr=ee.useEffect,rr=ee.useMemo,ir=ee.useDebugValue;Ot.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var o=tr(null);if(o.current===null){var s={hasValue:!1,value:null};o.current=s;}else s=o.current;o=rr(function(){function c(d){if(!l){if(l=!0,h=d,d=r(d),i!==void 0&&s.hasValue){var y=s.value;if(i(y,d))return v=y}return v=d}if(y=v,Qn(h,d))return y;var b=r(d);return i!==void 0&&i(y,b)?y:(h=d,v=b)}var l=!1,h,v,p=n===void 0?null:n;return [function(){return c(t())},p===null?void 0:function(){return c(p())}]},[t,n,r,i]);var a=er(e,o[0],o[1]);return nr(function(){s.hasValue=!0,s.value=a;},[a]),ir(a),a};(function(e){e.exports=Ot;})(Fn);const or=Bn(_e),{useSyncExternalStoreWithSelector:sr}=or;function ar(e,t=e.getState,n){const r=sr(e.subscribe,e.getState,e.getServerState||e.getState,t,n);return we(r),r}const Xe=e=>{({BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1}&&"production")!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?zn(e):e,n=(r,i)=>ar(t,r,i);return Object.assign(n,t),n},zt=e=>e?Xe(e):Xe;function Me(e,t){if(Object.is(e,t))return !0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return !1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return !1;for(const[r,i]of e)if(!Object.is(i,t.get(r)))return !1;return !0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return !1;for(const r of e)if(!t.has(r))return !1;return !0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return !1;for(let r=0;r<n.length;r++)if(!Object.prototype.hasOwnProperty.call(t,n[r])||!Object.is(e[n[r]],t[n[r]]))return !1;return !0}const cr=e=>{const t=e;t.use={};for(const n of Object.keys(t.getState())){const r=t.use;r[n]=()=>t(i=>i[n]);}return t},Bt="https://api.dandanplay.net",ur=async e=>{const t=`${Bt}/api/v2/search/episodes?${new URLSearchParams(e)}`;return await(await fetch(t)).json()};var E;(function(e){e[e.None=0]="None",e[e.Simplified=1]="Simplified",e[e.Traditional=2]="Traditional";})(E||(E={}));var me;(function(e){e[e.ltr=6]="ltr",e[e.rtl=1]="rtl",e[e.top=5]="top",e[e.bottom=4]="bottom";})(me||(me={}));const lr=async(e,t)=>{var s,a,c;const n={from:((s=t.from)==null?void 0:s.toString())??"0",withRelated:((a=t.withRelated)==null?void 0:a.toString())??"false",chConvert:((c=t.chConvert)==null?void 0:c.toString())??"0"},r=`${Bt}/api/v2/comment/${e.toString()}?${new URLSearchParams(n)}`;return await(await fetch(r)).json()};var dr=function(){if(typeof document>"u")return "transform";for(var e=["oTransform","msTransform","mozTransform","webkitTransform","transform"],t=document.createElement("div").style,n=0;n<e.length;n++)if(e[n]in t)return e[n];return "transform"}();function fr(e){var t=document.createElement("div");if(t.style.cssText="position:absolute;",typeof e.render=="function"){var n=e.render();if(n instanceof HTMLElement)return t.appendChild(n),t}if(t.textContent=e.text,e.style)for(var r in e.style)t.style[r]=e.style[r];return t}function hr(){var e=document.createElement("div");return e.style.cssText="overflow:hidden;white-space:nowrap;transform:translateZ(0);",e}function _r(e){for(var t=e.lastChild;t;)e.removeChild(t),t=e.lastChild;}function pr(e,t,n){e.style.width=t+"px",e.style.height=n+"px";}function mr(){}function vr(e,t){var n=document.createDocumentFragment(),r=0,i=null;for(r=0;r<t.length;r++)i=t[r],i.node=i.node||fr(i),n.appendChild(i.node);for(t.length&&e.appendChild(n),r=0;r<t.length;r++)i=t[r],i.width=i.width||i.node.offsetWidth,i.height=i.height||i.node.offsetHeight;}function gr(e,t){t.node.style[dr]="translate("+t.x+"px,"+t.y+"px)";}function yr(e,t){e.removeChild(t.node),this.media||(t.node=null);}var br={name:"dom",init:hr,clear:_r,resize:pr,framing:mr,setup:vr,render:gr,remove:yr},D=typeof window<"u"&&window.devicePixelRatio||1,ce=Object.create(null);function wr(e,t){if(ce[e])return ce[e];var n=12,r=/(\d+(?:\.\d+)?)(px|%|em|rem)(?:\s*\/\s*(\d+(?:\.\d+)?)(px|%|em|rem)?)?/,i=e.match(r);if(i){var o=i[1]*1||10,s=i[2],a=i[3]*1||1.2,c=i[4];s==="%"&&(o*=t.container/100),s==="em"&&(o*=t.container),s==="rem"&&(o*=t.root),c==="px"&&(n=a),c==="%"&&(n=o*a/100),c==="em"&&(n=o*a),c==="rem"&&(n=t.root*a),c===void 0&&(n=o*a);}return ce[e]=n,n}function Sr(e,t){if(typeof e.render=="function"){var n=e.render();if(n instanceof HTMLCanvasElement)return e.width=n.width,e.height=n.height,n}var r=document.createElement("canvas"),i=r.getContext("2d"),o=e.style||{};o.font=o.font||"10px sans-serif",o.textBaseline=o.textBaseline||"bottom";var s=o.lineWidth*1;s=s>0&&s!==1/0?Math.ceil(s):!!o.strokeStyle*1,i.font=o.font,e.width=e.width||Math.max(1,Math.ceil(i.measureText(e.text).width)+s*2),e.height=e.height||Math.ceil(wr(o.font,t))+s*2,r.width=e.width*D,r.height=e.height*D,i.scale(D,D);for(var a in o)i[a]=o[a];var c=0;switch(o.textBaseline){case"top":case"hanging":c=s;break;case"middle":c=e.height>>1;break;default:c=e.height-s;}return o.strokeStyle&&i.strokeText(e.text,s,c),i.fillText(e.text,s,c),r}function Qe(e){return window.getComputedStyle(e,null).getPropertyValue("font-size").match(/(.+)px/)[1]*1}function kr(e){var t=document.createElement("canvas");return t.context=t.getContext("2d"),t._fontSize={root:Qe(document.getElementsByTagName("html")[0]),container:Qe(e)},t}function Cr(e,t){e.context.clearRect(0,0,e.width,e.height);for(var n=0;n<t.length;n++)t[n].canvas=null;}function xr(e,t,n){e.width=t*D,e.height=n*D,e.style.width=t+"px",e.style.height=n+"px";}function Er(e){e.context.clearRect(0,0,e.width,e.height);}function Mr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.canvas=Sr(r,e._fontSize);}}function Dr(e,t){e.context.drawImage(t.canvas,t.x*D,t.y*D);}function Ir(e,t){t.canvas=null;}var Tr={name:"canvas",init:kr,clear:Cr,resize:xr,framing:Er,setup:Mr,render:Dr,remove:Ir};function Nr(e){var t=this,n=this.media?this.media.currentTime:Date.now()/1e3,r=this.media?this.media.playbackRate:1;function i(d,y){if(y.mode==="top"||y.mode==="bottom")return n-d.time<t._.duration;var b=t._.width+d.width,k=b*(n-d.time)*r/t._.duration;if(d.width>k)return !0;var Yt=t._.duration+d.time-n,Jt=t._.width+y.width,Zt=t.media?y.time:y._utc,Xt=Jt*(n-Zt)*r/t._.duration,Qt=t._.width-Xt,en=t._.duration*Qt/(t._.width+y.width);return Yt>en}for(var o=this._.space[e.mode],s=0,a=0,c=1;c<o.length;c++){var l=o[c],h=e.height;if((e.mode==="top"||e.mode==="bottom")&&(h+=l.height),l.range-l.height-o[s].range>=h){a=c;break}i(l,e)&&(s=c);}var v=o[s].range,p={range:v+e.height,time:this.media?e.time:e._utc,width:e.width,height:e.height};return o.splice(s+1,a-s-1,p),e.mode==="bottom"?this._.height-e.height-v%this._.height:v%(this._.height-e.height)}function $r(e,t,n,r){return function(){e(this._.stage);var i=Date.now()/1e3,o=this.media?this.media.currentTime:i,s=this.media?this.media.playbackRate:1,a=null,c=0,l=0;for(l=this._.runningList.length-1;l>=0;l--)a=this._.runningList[l],c=this.media?a.time:a._utc,o-c>this._.duration&&(r(this._.stage,a),this._.runningList.splice(l,1));for(var h=[];this._.position<this.comments.length&&(a=this.comments[this._.position],c=this.media?a.time:a._utc,!(c>=o));){if(o-c>this._.duration){++this._.position;continue}this.media&&(a._utc=i-(this.media.currentTime-a.time)),h.push(a),++this._.position;}for(t(this._.stage,h),l=0;l<h.length;l++)a=h[l],a.y=Nr.call(this,a),this._.runningList.push(a);for(l=0;l<this._.runningList.length;l++){a=this._.runningList[l];var v=this._.width+a.width,p=v*(i-a._utc)*s/this._.duration;a.mode==="ltr"&&(a.x=p-a.width+.5|0),a.mode==="rtl"&&(a.x=this._.width-p+.5|0),(a.mode==="top"||a.mode==="bottom")&&(a.x=this._.width-a.width>>1),n(this._.stage,a);}}}var et=typeof window<"u"&&(window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(e){return setTimeout(e,50/3)},Pr=typeof window<"u"&&(window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.webkitCancelAnimationFrame)||clearTimeout;function At(e,t,n){for(var r=0,i=0,o=e.length;i<o-1;)r=i+o>>1,n>=e[r][t]?i=r:o=r;return e[i]&&n<e[i][t]?i:o}function Ft(e){return /^(ltr|top|bottom)$/i.test(e)?e.toLowerCase():"rtl"}function U(){var e=9007199254740991;return [{range:0,time:-e,width:e,height:0},{range:e,time:e,width:0,height:0}]}function Vt(e){e.ltr=U(),e.rtl=U(),e.top=U(),e.bottom=U();}function De(){if(!this._.visible||!this._.paused)return this;if(this._.paused=!1,this.media)for(var e=0;e<this._.runningList.length;e++){var t=this._.runningList[e];t._utc=Date.now()/1e3-(this.media.currentTime-t.time);}var n=this,r=$r(this._.engine.framing.bind(this),this._.engine.setup.bind(this),this._.engine.render.bind(this),this._.engine.remove.bind(this));function i(){r.call(n),n._.requestID=et(i);}return this._.requestID=et(i),this}function Ie(){return !this._.visible||this._.paused?this:(this._.paused=!0,Pr(this._.requestID),this._.requestID=0,this)}function Te(){if(!this.media)return this;this.clear(),Vt(this._.space);var e=At(this.comments,"time",this.media.currentTime);return this._.position=Math.max(0,e-1),this}function Or(e){e.play=De.bind(this),e.pause=Ie.bind(this),e.seeking=Te.bind(this),this.media.addEventListener("play",e.play),this.media.addEventListener("pause",e.pause),this.media.addEventListener("playing",e.play),this.media.addEventListener("waiting",e.pause),this.media.addEventListener("seeking",e.seeking);}function Lr(e){this.media.removeEventListener("play",e.play),this.media.removeEventListener("pause",e.pause),this.media.removeEventListener("playing",e.play),this.media.removeEventListener("waiting",e.pause),this.media.removeEventListener("seeking",e.seeking),e.play=null,e.pause=null,e.seeking=null;}function Rr(e){this._={},this.container=e.container||document.createElement("div"),this.media=e.media,this._.visible=!0,this.engine=(e.engine||"DOM").toLowerCase(),this._.engine=this.engine==="canvas"?Tr:br,this._.requestID=0,this._.speed=Math.max(0,e.speed)||144,this._.duration=4,this.comments=e.comments||[],this.comments.sort(function(n,r){return n.time-r.time});for(var t=0;t<this.comments.length;t++)this.comments[t].mode=Ft(this.comments[t].mode);return this._.runningList=[],this._.position=0,this._.paused=!0,this.media&&(this._.listener={},Or.call(this,this._.listener)),this._.stage=this._.engine.init(this.container),this._.stage.style.cssText+="position:relative;pointer-events:none;",this.resize(),this.container.appendChild(this._.stage),this._.space={},Vt(this._.space),(!this.media||!this.media.paused)&&(Te.call(this),De.call(this)),this}function zr(){if(!this.container)return this;Ie.call(this),this.clear(),this.container.removeChild(this._.stage),this.media&&Lr.call(this,this._.listener);for(var e in this)Object.prototype.hasOwnProperty.call(this,e)&&(this[e]=null);return this}var q=["mode","time","text","render","style"];function Br(e){if(!e||Object.prototype.toString.call(e)!=="[object Object]")return this;for(var t={},n=0;n<q.length;n++)e[q[n]]!==void 0&&(t[q[n]]=e[q[n]]);if(t.text=(t.text||"").toString(),t.mode=Ft(t.mode),t._utc=Date.now()/1e3,this.media){var r=0;t.time===void 0?(t.time=this.media.currentTime,r=this._.position):(r=At(this.comments,"time",t.time),r<this._.position&&(this._.position+=1)),this.comments.splice(r,0,t);}else this.comments.push(t);return this}function Ar(){return this._.visible?this:(this._.visible=!0,this.media&&this.media.paused?this:(Te.call(this),De.call(this),this))}function Fr(){return this._.visible?(Ie.call(this),this.clear(),this._.visible=!1,this):this}function Vr(){return this._.engine.clear(this._.stage,this._.runningList),this._.runningList=[],this}function Hr(){return this._.width=this.container.offsetWidth,this._.height=this.container.offsetHeight,this._.engine.resize(this._.stage,this._.width,this._.height),this._.duration=this._.width/this._.speed,this}var Wr={get:function(){return this._.speed},set:function(e){return typeof e!="number"||isNaN(e)||!isFinite(e)||e<=0?this._.speed:(this._.speed=e,this._.width&&(this._.duration=this._.width/e),e)}};function $(e){e&&Rr.call(this,e);}$.prototype.destroy=function(){return zr.call(this)};$.prototype.emit=function(e){return Br.call(this,e)};$.prototype.show=function(){return Ar.call(this)};$.prototype.hide=function(){return Fr.call(this)};$.prototype.clear=function(){return Vr.call(this)};$.prototype.resize=function(){return Hr.call(this)};Object.defineProperty($.prototype,"speed",Wr);const Ht=e=>{const[t,n,r,i]=e.split(",");return {time:parseFloat(t),mode:me[parseInt(n)],color:`#${`000000${parseInt(r).toString(16)}`.slice(-6)}`,uid:parseInt(i)}},Wt=(e,t)=>e.map(n=>{const{p:r,m:i}=n,{time:o,mode:s,color:a}=Ht(r);return {text:i,mode:s,time:o,style:{fontSize:`${t.fontSize}px`,color:`#${a}`,opacity:`${t.opacity}`,textShadow:a==="00000"?"-1px -1px #fff, -1px 1px #fff, 1px -1px #fff, 1px 1px #fff":"-1px -1px #000, -1px 1px #000, 1px -1px #000, 1px 1px #000",fontFamily:`${t.fontFamily}`}}}),jt=e=>new $(e),tt={instance:null,media:null,container:null,comments:null},jr={show:!0,filters:[],userFilters:[],speed:1,style:{opacity:1,fontSize:25,fontFamily:"sans-serif"}};zt((e,t)=>({engine:tt,config:jr,create:(n,r,i,o)=>{o??(o=t().config);const s=Wt(i,o.style),a=jt({container:n,media:r,comments:s}),c={...t().engine,container:n,media:r,comments:i,instance:a};return e({engine:c}),c},recreate:(n,r)=>{const{container:i,media:o,comments:s}=t().engine;return !i||!o||!n&&!s?null:t().create(i,o,n??s,r)},destroy:()=>{const{instance:n}=t().engine;n==null||n.destroy(),e({engine:tt});},updateConfig:n=>{const{instance:r}=t().engine;if(!r)return;const{style:i,...o}=n;e({config:{...t().config,...o}});}}));const Ne=(e,t,n=!1)=>{let r=null,i;return (...s)=>{const a=()=>{r=null,n||(i=e(...s));},c=n&&!r;return r&&clearTimeout(r),r=setTimeout(a,t),c&&(i=e(...s)),i}},Z={chConvert:E.None,autoFetch:!1,enabled:!0,filters:[],userFilters:[],speed:1,style:{opacity:1,fontSize:25,fontFamily:"sans-serif"}},ve={config:Z,engine:null,media:null,container:null,comments:null};Object.freeze(Z);Object.freeze(ve);const Ut="danmaku-config",Ur=()=>{const e=localStorage.getItem(Ut);if(e){const t=JSON.parse(e),n={...Z};for(const r in n)Object.hasOwn(t,r)&&(n[r]=t[r]);return n}return {...Z}},qr=Ne(e=>{localStorage.setItem(Ut,JSON.stringify(e));},1e3),Kr=(e,t)=>({danmaku:{...ve,config:Ur()},_fetchCommentsWithCache:async(n,r)=>{const i=t().db,o=await(i==null?void 0:i.get("danmaku",n));return o?(f.debug("Danmaku cache hit, using cache",o),e(s=>({danmaku:{...s.danmaku,comments:o.comments}})),o.comments):(f.debug("Danmaku cache miss, falling back to api request"),t().fetchComments(n,r,!1))},fetchComments:async(n,r,i=!0)=>{var a;if(i)return t()._fetchCommentsWithCache(n,r);const{chConvert:o}=t().danmaku.config;r.chConvert??(r.chConvert=o),f.debug("Dispatching damaku request",n,r);const s=await lr(n,r);return f.debug("Danmaku result",s),e(c=>({danmaku:{...c.danmaku,comments:s.comments}})),s.comments.length>0&&await((a=t().db)==null?void 0:a.put("danmaku",{comments:s.comments,time:Date.now(),params:r,episodeId:n})),s.comments},updateDanmakuConfig:n=>{const i={...t().danmaku.config,...n};qr(i),e(o=>({danmaku:{...o.danmaku,config:i}}));},updateDanmakuStyle:n=>{const{config:r}=t().danmaku,i={...r,style:{...r.style,...n}};t().updateDanmakuConfig(i),t().recreateDanmaku();},createDanmaku:(n,r)=>{f.debug("Creating danmaku engine");const{comments:i,config:o}=t().danmaku;if(!i)throw new Error("Trying to create danmaku engine without comments");t().destroyDanmaku();const s=jt({container:n,media:r,comments:Wt(i,o.style)});return s.speed=o.speed,o.enabled||s.hide(),e(a=>({danmaku:{...a.danmaku,engine:s,container:n,media:r}})),s},toggleDanmaku:()=>{const{engine:n,config:r}=t().danmaku;t().updateDanmakuConfig({enabled:!r.enabled}),r.enabled?n==null||n.hide():n==null||n.show();},setDanmakuSpeed:n=>{const{engine:r}=t().danmaku;t().updateDanmakuConfig({speed:n}),r&&(r.speed=n);},resetDanmaku:()=>{t().destroyDanmaku(),e(n=>({danmaku:{...ve,config:n.danmaku.config}}));},recreateDanmaku:Ne(()=>{const{container:n,media:r}=t().danmaku;!n||!r||t().createDanmaku(n,r);},200),destroyDanmaku:()=>{const{engine:n}=t().danmaku;n==null||n.destroy(),e(r=>({danmaku:{...r.danmaku,engine:null}}));}}),ge={meta:{key:null,title:""},results:[],error:null,selected:null,episode:null};Object.freeze(ge);const qt="danmaku-media-cache",Kt=()=>{const e=localStorage.getItem(qt);return JSON.parse(e||"{}")},Gr=e=>Kt()[e]??null,Yr=(e,t)=>{const n=Kt();localStorage.setItem(qt,JSON.stringify({...n,[e]:t}));},Jr=(e,t)=>({isMediaActive:!1,media:{...ge},setIsMediaActive:n=>e({isMediaActive:n}),updateMedia:n=>{e(r=>({media:{...r.media,...n}}));},fetchMediaInfo:async n=>{f.debug("Dispatching search request",{anime:n});const{animes:r,errorMessage:i,success:o}=await ur({anime:n});return f.debug("Search result",{animes:r,errorMessage:i,success:o}),o?(t().resetMedia(),r.length===0?t().updateMedia({results:[],error:`No result found for title ${n}`}):t().updateMedia({results:r.sort((s,a)=>s.animeId-a.animeId),error:null}),r):(t().updateMedia({error:i}),[])},setTitle:n=>{t().updateMedia({meta:{...t().media.meta,title:n}});},resetMedia:()=>{e(n=>({media:{...ge,meta:n.media.meta}}));},setTitleMap:(n,r)=>{Yr(n,r);},getTitleMap:n=>Gr(n)}),Zr=zt((e,t,n)=>({db:null,openDb:async()=>{try{const r=await On("danmaku",1,{upgrade:(i,o,s)=>{f.log(`Upgrading indexedDB from ${o} to ${s}`),i.createObjectStore("danmaku",{keyPath:"episodeId"}).createIndex("byTime","time");},blocked:()=>{f.log("IndexedDB is blocked, please close other tabs");},blocking:()=>{f.log("Page is blocking indexedDB, please refresh");}});r.onerror=i=>{f.error(i);},e({db:r});}catch(r){f.error("Error opening indexedDB"),f.error(r);}},...Jr(e,t),...Kr(e,t)})),m=cr(Zr),V=()=>m(e=>e.media,Me),te=()=>m(e=>e.danmaku,Me);window.useStore=m;const Xr=()=>{const e=m.use.createDanmaku(),[t,n,r]=xn(),{comments:i}=te(),o=m.use.recreateDanmaku(),s=m.use.destroyDanmaku();w(()=>{!t||!n||!i||e(t,n);},[t,n,i]),w(()=>{r?o():s();},[r]);},Qr=()=>{w(()=>{f.debug("Creating danmaku portal root");const e=document.getElementById("danmaku-portal");e&&e.remove();const t=document.createElement("div");t.id="danmaku-portal",document.body.prepend(t);},[]);},Gt=(e,t)=>e?!t||e.title!==t.title?"title":e.season!==t.season?"season":e.episode!==t.episode?"episode":"none":t?"title":"none",ei=()=>{const e=document.querySelector("title"),t=/(?<title>[^\s]+)\s*-\s*S(?<season>\d+)\s*·\s*E(?<episode>\d+)/;if(!(e!=null&&e.textContent))return null;const n=t.exec(e.textContent);return n?{title:n.groups.title,season:parseInt(n.groups.season),episode:parseInt(n.groups.episode)}:null},ti=(e,t,n)=>{if(e.length===0)return null;const r=e.filter(o=>o.animeTitle.includes(t));if(r.length>0)return r[n-1]??r[0];const i=e[n-1];return i||e[0]},ni=(e,t)=>{const n=e.episodes[t-1];return n||(e.episodes[0]?e.episodes[0]:null)},ri=()=>{const e=document.querySelector("div[class='Player-fullPlayerContainer-wBDz23']"),t=document.querySelector("video");return {container:e,media:t}},ii=e=>{const{meta:t}=V(),n=m.use.resetMedia(),r=m.use.fetchMediaInfo(),i=m.use.getTitleMap(),o=m.use.updateMedia(),s=m.use.fetchComments(),a=x(e);w(()=>{if(!e)return;const{episode:c,title:l,season:h}=e,v=Gt(a.current,e);f.debug(`Change type ${v}`);const p=async()=>{if(!t.key)throw new Error("meta.key is undefined, this is likely a bug");const b=i(t.key);let k;if(f.debug(`Title changed to ${l}, fetching media info`),b?(f.debug(`Using mapped title ${t.key} -> ${b}`),k=await r(b)):k=await r(l),k.length===0){f.warn(`No results found for title ${l}, check if title is parsed correctly or do a manual search`);return}await d(k);},d=async b=>{const k=ti(b,l,h);if(!k){f.warn("No show matched, please select one manually");return}o({selected:k}),await y(k);},y=async b=>{const k=ni(b,c);if(!k){f.warn("No episode matched, is the episode number correct?");return}o({episode:k}),await s(k.episodeId,{withRelated:!0},!0);};switch(v){case"title":n(),f.debug(`Title changed to ${l}, fetching media info`),p();break;case"season":{f.debug(`Season changed to ${h}`);const b=m.getState().media.results;d(b);break}case"episode":{f.debug(`Episode changed to ${c}`);const b=m.getState().media.selected;if(!b){f.warn("Episode change is detected but no show is selected. This is likely a bug");return}y(b);break}default:f.warn(`Change type ${v} should not be reached`);break}a.current=e;},[e]);},oi=()=>document.querySelector("div[class='PlayerControls-buttonGroupCenter-LDbSmK PlayerControls-buttonGroup-L3xlI0 PlayerControls-balanceLeft-jE50ih']"),si="AudioVideoFullPlayer-bottomBar-h224iE",ai=()=>{const[e,t]=C(null),n=x(null),r=A(()=>new MutationObserver(i=>{for(const o of i){for(const a of o.addedNodes)if(a instanceof HTMLElement&&a.classList.contains(si)){n.current=a;const c=oi();if(!c){f.error("Mount point not found, aborting"),f.error("Possibly the class name has changed");return}t(c);}const s=Array.from(o.removedNodes);(s.includes(n.current)||s.some(a=>a.contains(n.current)))&&(n.current=null,t(null));}}),[]);return w(()=>(r.observe(document.body,{childList:!0,subtree:!0}),()=>{r.disconnect();}),[]),e},ci=()=>{const[e,t]=C(null),n=m.use.setIsMediaActive(),r=m.use.updateMedia(),i=document.querySelector("title"),o=A(()=>new MutationObserver(()=>{const s=ei();s?(n(!0),r({meta:{key:`${s.title} - ${s.season}`,title:s.title}})):n(!1),t(a=>s?a&&Gt(a,s)==="none"?a:s:a);}),[]);return w(()=>{if(i)return o.observe(i,{childList:!0}),()=>{o.disconnect();}},[i]),e},ui="_buttonWarning_1352t_1",li="_toolbar_1352t_16",di="_popupPanel_1352t_24",ye={buttonWarning:ui,toolbar:li,popupPanel:di};var fi=0;function u(e,t,n,r,i,o){var s,a,c={};for(a in t)a=="ref"?s=t[a]:c[a]=t[a];var l={type:e,props:c,key:n,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--fi,__source:i,__self:o};if(typeof e=="function"&&(s=e.defaultProps))for(a in s)c[a]===void 0&&(c[a]=s[a]);return preact.options.vnode&&preact.options.vnode(l),l}const hi=F(({children:e,warning:t,...n},r)=>{const i=`PlayerIconButton-playerButton-zDmEsI IconButton-button-s4bVCh Link-link-SxPFpG Link-default-BXtKLo ${t?ye.buttonWarning:""}`;return u("div",{children:[u("button",{...n,ref:r,className:i,children:e}),t&&u("div",{})]})}),_i=({onClickaway:e,children:t})=>{const n=x(null);return w(()=>{const r=i=>{n.current&&!n.current.contains(i.target)&&e(i);};return document.addEventListener("click",r),()=>{document.removeEventListener("click",r);}},[e]),t(n)},pi=()=>u("svg",{viewBox:"0 0 400 400",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1713",width:"32",height:"24",children:[u("path",{id:"svg_10",fill:"currentColor",d:"m228.82,254.62c-10.19,1.42 -19.55,2.67 -28.89,4.04c-11.98,1.75 -23.89,4.18 -35.92,5.21c-8.4,0.72 -15.77,-3.54 -22.31,-8.64c-3.15,-2.46 -2.75,-4.64 1.11,-5.42c5.01,-1.01 10.18,-1.35 15.29,-1.78c21.2,-1.79 42.4,-3.49 63.59,-5.27c2.14,-0.18 4.26,-0.68 6.74,-1.09c0,-12.05 0,-24.09 0,-36.8c-9.94,1.47 -19.61,2.81 -29.23,4.44c-1.13,0.19 -2.09,1.89 -2.97,3.01c-2.74,3.51 -5.13,3.8 -7.33,-0.1c-2.82,-5.01 -5.16,-10.38 -7,-15.84c-5.52,-16.35 -10.5,-32.89 -16.13,-49.21c-1.99,-5.77 -5.26,-11.09 -7.89,-16.64c-2.73,-5.77 -1.28,-8.26 5.08,-7.86c7.93,0.49 15.95,2.84 23.7,2.06c21.29,-2.13 42.5,-5.21 63.66,-8.43c10.14,-1.54 20.99,-2.41 29.93,-6.82c13.96,-6.9 24.73,-1.48 35.99,4.93c5.76,3.28 11.56,6.62 16.83,10.6c4.42,3.34 4.43,7.07 0.13,10.4c-11.04,8.53 -16.25,20.73 -21.88,32.82c-4.27,9.17 -9.05,18.11 -13.81,27.04c-1.23,2.31 -3.02,4.42 -4.91,6.27c-3.7,3.62 -6.48,3.53 -9.49,-0.69c-1.76,-2.47 -3.51,-2.93 -6.24,-2.52c-10.01,1.49 -20.04,2.8 -30.51,4.24l0,36.4c7.45,-0.84 14.82,-1.6 22.16,-2.53c17.46,-2.21 34.88,-4.72 52.37,-6.68c12.18,-1.36 22.99,2.6 32.28,10.48c1.52,1.29 2.29,3.49 3.4,5.26c-1.8,0.98 -3.52,2.52 -5.43,2.82c-4.24,0.69 -8.58,0.96 -12.89,1.04c-16.81,0.32 -33.63,0.2 -50.42,0.82c-13.76,0.5 -27.5,1.82 -41.46,2.79c-1.92,38.38 3.65,77.07 -8.81,114.11c-0.7,0.1 -1.4,0.2 -2.1,0.3c-0.85,-1.76 -2.25,-3.46 -2.47,-5.3c-1.3,-10.87 -2.99,-21.75 -3.31,-32.66c-0.7,-23.46 -0.65,-46.94 -0.9,-70.42c0.03,-1.12 0.04,-2.25 0.04,-4.38zm17.35,-62.21c10.34,-1.56 20.14,-3.35 30.02,-4.41c5.77,-0.62 8.95,-2.71 10.55,-8.64c3.79,-14.06 9,-27.81 9.13,-42.61c0.08,-9.17 -4.37,-14 -13.4,-13.43c-11.22,0.72 -22.38,2.48 -33.54,4.03c-1.1,0.15 -2.76,1.97 -2.8,3.07c-0.26,6.94 -0.13,13.89 -0.13,19.46c8.32,0 16.1,-0.17 23.86,0.07c4.49,0.14 5.93,3.3 2.99,6.68c-1.77,2.04 -4.29,3.8 -6.82,4.74c-6.46,2.4 -13.13,4.27 -19.86,6.4l0,24.64zm-18.77,-63.22c-14.18,2.03 -27.58,3.95 -41.36,5.92c3,21.9 5.91,43.17 8.89,64.94c10.88,-1.47 21.06,-2.77 31.2,-4.31c1.01,-0.15 2.53,-1.77 2.56,-2.74c0.21,-7.46 0.12,-14.92 0.12,-22.31c-9.18,-2.64 -18.91,-1.41 -27.66,-7.13c8.67,-6 18.44,-6.23 27.88,-9.55c-0.53,-8.05 -1.06,-16.21 -1.63,-24.82z"}),u("path",{id:"svg_11",fill:"currentColor",d:"m58.31,306.77c4.37,0.43 8.74,0.9 13.11,1.29c3.97,0.36 7.95,0.56 11.91,0.95c7.81,0.77 12.83,-3 16.65,-9.47c6.85,-11.6 9.36,-24.45 10.75,-37.5c1,-9.4 1.29,-18.94 1.07,-28.4c-0.31,-13.62 -6.68,-18.56 -19.72,-15.2c-12.57,3.25 -24.87,7.38 -34.83,16.42c-3.76,3.41 -6.12,2.71 -8.52,-1.96c-4.56,-8.86 -2.27,-17.1 7.14,-23.13c7.94,-5.08 12.81,-12.04 14.57,-21.12c2.6,-13.39 1.71,-26.45 -4.5,-38.87c-0.82,-1.63 -1.5,-3.34 -2.16,-5.04c-0.35,-0.9 -0.53,-1.87 -1.03,-3.72c2.15,0 3.86,-0.24 5.46,0.06c1.93,0.37 3.93,0.94 5.64,1.88c14.81,8.19 27.91,-0.39 41.46,-4.2c0.79,-0.22 1.47,-1.64 1.79,-2.63c4.36,-13.49 7.24,-27.28 7.69,-41.49c0.22,-7.01 -1.15,-8.22 -8.29,-6.82c-10.42,2.04 -20.78,4.39 -31.12,6.77c-10.92,2.51 -19.22,-0.68 -24.84,-10.52c2.45,-0.4 4.53,-0.94 6.64,-1.06c16.46,-0.94 32.78,-2.86 48.48,-8.11c4.02,-1.34 7.96,-3.47 11.33,-6.03c4.98,-3.79 10.1,-4.43 15.4,-1.77c6.38,3.2 12.58,6.8 18.64,10.57c4.3,2.68 4.39,5.67 0.86,9.42c-0.68,0.73 -1.38,1.49 -2.22,2.01c-16.42,10.18 -21.91,27.39 -28.06,44.07c-0.56,1.51 0.67,4.01 1.65,5.71c4.49,7.7 3.29,10 -5.51,11.32c-12.48,1.87 -24.91,4.02 -37.32,6.29c-1.3,0.24 -3.16,1.99 -3.31,3.22c-1.93,15.95 -3.61,31.94 -5.41,48.5c9.19,-1.16 18.53,-2.27 26.07,-8.18c5.75,-4.51 11.22,-5.33 17.44,-1.86c3.34,1.86 6.69,3.74 9.85,5.88c3.26,2.21 4.19,5.52 1.74,8.63c-6.83,8.66 -7.67,18.94 -8.36,29.23c-0.98,14.6 -1.27,29.26 -2.48,43.84c-1.46,17.49 -5.62,34.24 -18.17,47.48c-3.36,3.54 -7.68,6.3 -11.9,8.86c-3.98,2.42 -8.17,0.97 -9.62,-3.04c-4.8,-13.24 -14.71,-21.45 -26.09,-28.47c-0.98,-0.61 -2,-1.16 -3,-1.74c0.39,-0.7 0.76,-1.38 1.12,-2.07z"}),u("path",{id:"svg_12",fill:"currentColor",d:"m254.44,107.14c2.2,-5.96 4.24,-11.98 6.62,-17.87c4.48,-11.09 9.53,-22.01 8.9,-34.38c-0.14,-2.78 -0.61,-5.59 -1.32,-8.29c-1.16,-4.41 -0.09,-6.88 4.5,-7.33c3.89,-0.37 8.09,-0.28 11.81,0.8c6.45,1.88 12.77,4.39 18.87,7.21c2.9,1.34 4.43,4.11 1.57,7.32c-13.6,15.25 -27.14,30.56 -40.81,45.76c-2.57,2.86 -5.6,5.29 -8.42,7.92c-0.58,-0.38 -1.15,-0.76 -1.72,-1.14z"}),u("path",{id:"svg_13",fill:"currentColor",d:"m227.27,87.26c-0.15,1.32 -0.16,2.66 -0.46,3.94c-2.36,10.27 -8.2,12.4 -16.37,5.77c-12.11,-9.83 -18.57,-23.59 -25.19,-37.15c-1.57,-3.21 0.24,-5.12 3.91,-4.53c14.24,2.3 25.94,9.03 34.27,20.89c2.18,3.11 3.1,7.12 4.59,10.72c-0.26,0.12 -0.5,0.24 -0.75,0.36z"})]}),mi=()=>u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",class:"bi bi-gear",viewBox:"0 0 16 16",children:[u("path",{d:"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"}),u("path",{d:"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"})]}),vi=()=>u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",class:"bi bi-info-circle",viewBox:"0 0 16 16",children:[u("path",{d:"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"}),u("path",{d:"m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"})]}),gi=()=>u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",class:"bi bi-eye",viewBox:"0 0 16 16",children:[u("path",{d:"M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"}),u("path",{d:"M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"})]}),yi=()=>u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",class:"bi bi-eye-slash",viewBox:"0 0 16 16",children:[u("path",{d:"M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"}),u("path",{d:"M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"}),u("path",{d:"M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12-.708.708z"})]}),bi="_baseInput_1e3hb_1",wi="_baseButton_1e3hb_14",Si="_disabled_1e3hb_23",ki="_baseScrollbar_1e3hb_27",Ci="_panel_1e3hb_43",xi="_sectionHeader_1e3hb_58",Ei="_chSelection_1e3hb_62",Mi="_sliderInput_1e3hb_72",Di="_seriesSelection_1e3hb_81 _baseScrollbar_1e3hb_27",Ii="_seriesSelectionChip_1e3hb_90 _baseButton_1e3hb_14",Ti="_titleSelection_1e3hb_104",Ni="_titleSelectionInput_1e3hb_109 _baseInput_1e3hb_1",$i="_episodeSelection_1e3hb_114 _baseInput_1e3hb_1",Pi="_episodeSelectionOptions_1e3hb_119",Oi="_danmakuListWrapper_1e3hb_125 _baseScrollbar_1e3hb_27",Li="_danmakuEntry_1e3hb_133",Ri="_time_1e3hb_138",zi="_message_1e3hb_142",S={baseInput:bi,baseButton:wi,disabled:Si,baseScrollbar:ki,panel:Ci,sectionHeader:xi,chSelection:Ei,sliderInput:Mi,seriesSelection:Di,seriesSelectionChip:Ii,titleSelection:Ti,titleSelectionInput:Ni,episodeSelection:$i,episodeSelectionOptions:Pi,danmakuListWrapper:Oi,danmakuEntry:Li,time:Ri,message:zi},$e=F(({children:e,...t},n)=>u("div",{className:S.panelWrapper,...t,ref:n,children:u("div",{className:S.panel,children:e})})),X=F(({children:e},t)=>u("div",{className:S.sectionHeader,ref:t,children:e})),Bi=e=>{switch(e){case"jpdrama":return "🎭";case"tvseries":return "📺";case"movie":return "🎬";case"ova":return "📼";case"web":return "🌐";case"musicvideo":return "🎵";default:return "❓"}},Ai=({media:e})=>{const{meta:t}=V(),n=m.use.updateMedia(),r=m.use.setTitleMap();return u("button",{onClick:()=>{n({selected:e,episode:e.episodes[0]??null}),t.key&&(f.log(`Mapped title ${t.key} -> ${e.animeTitle}`),r(t.key,e.animeTitle));},className:S.seriesSelectionChip,title:`${e.typeDescription} - ${e.animeTitle}`,children:[u("div",{children:Bi(e.type)}),u("div",{children:e.animeTitle})]})},Fi=()=>{const{results:e}=V();return u("div",{className:S.seriesSelection,children:e.map(t=>u(Ai,{media:t},t.animeId))})},Vi=()=>{const{selected:e,meta:t}=V(),[n,r]=C(t.title??""),i=m.use.fetchMediaInfo();w(()=>{e&&r(e.animeTitle);},[e]);const o=async()=>{await i(n);};return u("div",{className:S.titleSelection,children:[u("input",{className:S.titleSelectionInput,type:"text",value:n,onChange:s=>{(s==null?void 0:s.target)instanceof HTMLInputElement&&r(s.target.value);}}),u("button",{className:S.baseButton,onClick:o,children:"Search"})]})},Hi=()=>{const{selected:e,episode:t}=V(),n=m.use.updateMedia();return u("div",{children:u("select",{className:S.episodeSelection,value:t==null?void 0:t.episodeTitle,onChange:r=>{if((r==null?void 0:r.target)instanceof HTMLSelectElement){const i=r.target.value;n({episode:(e==null?void 0:e.episodes.find(o=>o.episodeTitle===i))??null});}},children:e==null?void 0:e.episodes.map(r=>u("option",{className:S.episodeSelectionOptions,children:r.episodeTitle},r.episodeId))})})},Wi=({getDanmakuContainer:e})=>{const t=m.use.createDanmaku(),n=m.use.fetchComments(),{episode:r}=V(),i=async()=>{if(!r)return;const{container:s,media:a}=e();if(!s||!a){f.error("Failed to find video element to mount danmaku");return}await n(r.episodeId,{withRelated:!0},!1),t(s,a);},o=r===null;return u($e,{children:[u(X,{children:"Title"}),u(Vi,{}),u(Fi,{}),u(X,{children:"Episode"}),u(Hi,{}),u("div",{children:u("button",{className:`${S.baseButton} ${o?S.disabled:""}`,onClick:i,disabled:o,children:"Fetch comments"})})]})},ji=e=>{const t=Math.floor(e/3600),n=Math.floor(e%3600/60),r=Math.floor(e%60).toString().padStart(2,"0");return t>0?`${t}:${n.toString().padStart(2,"0")}:${r}`:`${n}:${r}`},Ui=()=>{const{comments:e}=te(),t=e==null?void 0:e.map(n=>{const{time:r}=Ht(n.p);return {message:n.m,time:r}}).sort((n,r)=>n.time-r.time);return u($e,{children:[u(X,{children:"Comments"}),u("div",{className:S.danmakuListWrapper,children:[!(t!=null&&t.length)&&u("div",{children:"No comments loaded"}),(t==null?void 0:t.length)&&u("div",{children:[t.length," Comments loaded"]}),t==null?void 0:t.map(({time:n,message:r},i)=>u("div",{className:S.danmakuEntry,children:[u("div",{className:S.time,children:ji(n)}),u("div",{className:S.message,title:r,children:r})]},i))]})]})},ue=({title:e,value:t,onChange:n,...r})=>{const i=o=>{const s=o.target;!s||s.value===void 0||n==null||n(s.value);};return u("div",{className:S.sliderInput,children:[u("div",{children:e}),u("input",{...r,value:t,type:"range",onChange:i}),u("div",{children:t})]})},qi=()=>{const{config:e}=te(),t=m.use.setDanmakuSpeed(),n=m.use.updateDanmakuStyle(),r=m.use.updateDanmakuConfig(),{style:i}=e,o=l=>{n({opacity:parseInt(l)/100});},s=l=>{n({fontSize:parseInt(l)});},a=l=>{t(parseInt(l));},c=l=>{const h=l.target;!h||h.value===void 0||r({chConvert:parseInt(h.value)});};return u($e,{children:[u(X,{children:"Settings"}),u("div",{className:S.chSelection,children:[u("div",{children:[u("div",{children:"None"}),u("input",{type:"radio",name:"mode",value:E.None,checked:e.chConvert===E.None,onChange:c})]}),u("div",{children:[u("div",{children:"Chs"}),u("input",{type:"radio",name:"mode",value:E.Simplified,checked:e.chConvert===E.Simplified,onChange:c})]}),u("div",{children:[u("div",{children:"Cht"}),u("input",{type:"radio",name:"mode",value:E.Traditional,checked:e.chConvert===E.Traditional,onChange:c})]})]}),u(ue,{title:"Opacity",value:(i.opacity*100).toFixed(0),onChange:o,min:0,max:100}),u(ue,{title:"Font size",value:i.fontSize,onChange:s,min:10,max:100}),u(ue,{title:"Speed",value:e.speed,onChange:a,min:1,max:400})]})},Ki=e=>{const[t,n]=C(!1),r=x(null),i=x(null);return w(()=>{const o=new IntersectionObserver(s=>{s.forEach(a=>{a.isIntersecting?n(!0):n(!1);});},e);return r.current&&(o.observe(r.current),i.current=r.current),()=>{i.current&&o.unobserve(i.current);}},[r,e]),[t,r]},Gi=(...e)=>t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t);});},Y=F(({children:e,onClick:t},n)=>{const r=x(null),{results:i}=m(a=>a.media,Me),o=Gi(n,r);return u(hi,{onClick:()=>{if(r.current){const a=r.current.getBoundingClientRect();t==null||t({top:a.top,left:a.left});}},ref:o,warning:i.length===0,children:e})}),Yi=({onClick:e})=>{const{config:t}=te(),n=m.use.toggleDanmaku();return u(Y,{onClick:()=>{n(),e==null||e();},children:t.enabled?u(gi,{}):u(yi,{})})},Ji=()=>{const[e,t]=Ki(),[n,r]=C(null),[i,o]=C({top:0,left:0}),s=x(null),a=x(null),c=x(null),l=x(document.getElementById("danmaku-portal")),h=n!==null;if(!l.current)throw new Error("Danmaku portal root not found");w(()=>(f.debug("Mounting Plex toolbar"),()=>{f.debug("Unmounting Plex toolbar");}),[]),w(()=>{e||r(null);},[e]);const v=d=>{var y,b,k;(y=s.current)!=null&&y.contains(d.target)||(b=a.current)!=null&&b.contains(d.target)||(k=c.current)!=null&&k.contains(d.target)||r(null);},p=d=>y=>{if(n===d){r(null);return}r(d),o(y);};return u("div",{className:ye.toolbar,ref:t,children:[h&&Q(u(_i,{onClickaway:v,children:d=>u("div",{className:`${ye.popupPanel}`,style:{top:i.top,left:i.left,transform:`translateY(${e?"-100%":"100%"})`},ref:d,children:n==="danmaku"?u(Wi,{getDanmakuContainer:ri}):n==="settings"?u(qi,{}):u(Ui,{})})}),l.current),u(Y,{onClick:p("danmaku"),ref:s,children:u(pi,{})}),u(Y,{onClick:p("settings"),ref:a,children:u(mi,{})}),u(Yi,{}),u(Y,{onClick:p("info"),ref:c,children:u(vi,{})})]})},Zi=()=>{const e=m.use.resetDanmaku(),t=m.use.isMediaActive(),n=ai(),r=ci();return ii(r),Qr(),Xr(),w(()=>{r&&f.debug("Currently playing",r);},[r]),w(()=>{t||(f.debug("Video player lost, resetting danmaku"),e());},[t]),n&&Q(u(Ji,{}),n)},Xi=()=>{const e=m.use.openDb();return w(()=>{const t=new ResizeObserver(Ne(()=>{const n=m.getState().danmaku.engine;n&&(f.debug("Resizing danmaku engine"),n.resize());},200));return t.observe(document.body),()=>{t.disconnect();}},[]),w(()=>{f.debug("App loaded"),e();},[]),u(preact.Fragment,{children:u(Zi,{})})},Qi=e=>{preact.render(u(Xi,{}),(()=>{const t=document.createElement("div");return e.prepend(t),t})());};Qi(document.body);

})(preact);
