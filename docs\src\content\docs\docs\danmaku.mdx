---
title: 弹幕管理
description: 弹幕管理
---

### 弹幕搜索

搜索功能依赖以下弹幕源

##### [弹弹Play](https://www.dandanplay.com/)（默认）
#####  [B站](https://www.bilibili.com/)
#####  [腾讯视频](https://v.qq.com/)

搜索结果只包含影视类视频，不包含其他如用户上传的视频。

可以在设置页面中启用或禁用这些弹幕源。

未来会添加对更多弹幕源的支持。

:::caution[关于搜索]
除**弹弹Play**外，其他弹幕源的搜索使用的是非开放的接口，可能会受到以下因素的影响：

- 对应网站的登录状态
- 地域、代理、VPN 等网络因素

基本上，如果在网站上能够正常播放视频，那么扩展的搜索功能也应该能够正常使用。反之，如果发现搜索结果缺失，获取弹幕数量少于预期，可以尝试直接前往对应网站观看视频，看看是否有类似的问题。

同时，与这些弹幕源所有交互都会以当前登录账号的身份进行。比如如果启用了**B站**源，那么搜索结果和弹幕获取结果都会以当前登录的**B站**账号的身份进行。
:::

### 地址解析

扩展可以解析视频地址，从而获取该视频的弹幕。

支持的视频地址格式

- B站:
- https://www.bilibili.com/bangumi/play/ss12345
- https://www.bilibili.com/bangumi/play/ep12345
- 腾讯:
- https://v.qq.com/x/cover/abc123/def456.html

受到与**内置搜索**相同的限制，只支持影视类视频。

### 本地弹幕

在弹出界面的导入页面可以导入其他来源的弹幕文件(`.xml`格式)。

不支持 `.ass` 和 `.srt` 等“字幕”文件。这类文件一般通过 `.xml` 转换而来，导入时请使用原始文件。

扩展可以识别一些常见的格式，但不保证所有格式都能被识别。发现导入失败的情况请提交BUG或者[功能请求](https://github.com/Mr-Quin/danmaku-anywhere/issues/new)。

:::note
扩展不保存用户上传的原始文件，只保存解析后的弹幕数据。在导出时无法还原原始文件。
:::

### 导出（备份）

弹幕库不享受浏览器的同步功能，弹幕数据只保存在当前设备上。

导出可以作为一种备份方式，在不同设备间传递弹幕。导出的文件格式为 `.json`，为 Danmaku Anywhere 的专用格式。
