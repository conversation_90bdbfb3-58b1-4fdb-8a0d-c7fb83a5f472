---
title: 更新日志
description: 更新日志
---

## v1.0.0 - 2025-05-24

### 改动

- 重做部分UI
  - 弹幕装填页改用列表显示
  - 搜索和剧集列表页显示番剧封面 （以下载的剧集刷新后才有封面）
  - 优化本地弹幕分组
  - 优化错误信息显示
- 优化弹幕导入/导出流程
  - 导入功能移至单独的页面
  - 导入时不再需要区分弹幕类型
- 替换预设装填配置，新增配置库
  - 新预设默认在所有页面中启用扩展
  - 配置库 https://github.com/Mr-Quin/danmaku-anywhere-config

### 修复

- 放宽导入限制 #72
- 放宽DNR规则，修复Firefox中DNR有时不适用的问题 #70

此次更新改动较大，更新时如果出现问题请删除重装

## v0.20.4 - 2025-05-11

### 改动
- 修复导出弹幕时格式错误的问题

### 修复
- 修复一个XML弹幕文件导入失败的问题

## v0.20.3 - 2025-05-05

### 修复
- 修复一个XML弹幕文件导入失败的问题

## v0.20.2 - 2025-04-27

### 修复
- 修复顶部/底部弹幕不消失的问题

## v0.20.1 - 2025-04-14

### 修复
- 修复弹弹Play选项卡死的问题 #69
- 修复Bilibili选项按了没反应的问题
- 修复腾讯选项页面404的问题

## v0.20.0 - 2025-04-12

### 改动

- 替换弹幕渲染器，现在支持更多弹幕设置 #66 #27 #67


## v0.19.2 - 2025-04-08

### 修复

- 修复有时换集时报错的问题 #65


## v0.19.1 - 2025-02-14

### 修复

- 修复扩展在安卓Firefox中崩溃的问题

## v0.19.0 - 2025-02-09

### 新增

- AI 自动适配功能
  - 使用 AI 从网页中提取正在播放的番剧信息
  - 目前测试下来效果还不错，但是受网页结构影响较大，可能需要修正标题和集数。如果匹配结果一直有问题，请反馈问题。
  - **注意事项：**
    - 向服务器发送的信息中可能包含当前网页的 URL，如果是私人网站可能会暴露地址，请注意隐私。
    - 服务器端保留 7 天内的请求记录，用于调试和分析。
    - 由于使用的是免费的 AI 服务，调用次数限制较低，可能会出现调用失败的情况。
  - 如果无法使用 AI 自动适配，也可以手动编写适配规则。
- 一些 UI 上的优化

### 改动

- 移除弹弹Play 选项中的“使用新 API”开关

### 修复

- 修复一个检测不到`iframe`中文档刷新的问题
- 修复控件按钮可以拽出窗口外的问题

## v0.18.3 - 2025-02-08

### 修复

- 修复了部分 B站页面无法打开的问题
- 修复了一些 Firefox 兼容性问题
- 修复了一些 UI Bug

### 其他

- 升级了弹幕库，现在弹幕渲染应该比之前更流畅。

---

## v0.18.2 - 2025-02-04

### 修复

- 修复了简繁转换不适用的问题 (#63)
- 修复了缓存清理计时器可能失效的问题

---

## v0.18.1 - 2025-01-26

### 修复

- 修复了一个弹弹Play API 相关的 Bug
- 修复了在一些情况下插件升级时数据迁移失败的问题

---

## v0.18.0 - 2025-01-26

### 改动

- 调整了与弹弹Play 的交互方式 (#57)
  - 如果发现弹弹Play 相关功能出现问题，可以尝试在设置-弹幕来源-弹弹Play 中关闭“使用新 API”开关并反馈 BUG。
- 调整了弹弹Play 关联弹幕的获取方式 (#58)
- 适配配置中可以给规则添加“优先”标签 (#56)
- 优先使用带有优先标签的规则 (#54)
- 新增缓存设置功能，自动删除旧弹幕 (设置-缓存策略)
- 控件弹窗可以通过拖拽工具栏调整位置
- 控件弹窗可以锁定，锁定后点击窗口外不会关闭窗口

### 修复

- 修复了通知显示位置不正确的问题

---

## v0.17.2 - 2025-01-22

### 修复

- 暂时修复了弹弹Play 无法搜索的问题 (#57)

---

## v0.17.1 - 2025-01-09

### 修复

- 修复了扩展在 Firefox 中崩溃的问题 (#55)

---

## v0.17.0 - 2025-01-08

### 改动

- 优化了移动端 UI
- 通知可以同时显示多个
- 允许预装填弹幕（网页无视频时也可以装填，会吸附到第一个出现的视频上）
- 允许拖拽移动网页控件按钮
- 设置中增加了 debug 开关

### 修复

- 修复了移动端弹幕播放器无法启动的问题 (#52)
- 修复了特定标签页顺序下，使用弹出窗口装填弹幕会失败的问题
- 修复了剧集列表中排序不正确的问题
- 修复了一个弹幕装填失败时会提示成功的问题

---

## v0.16.2 - 2025-01-04

### 新增

- 增加了搜索时将搜索词转换为简体中文的开关 (#51)
  - 全局开关在设置-使用简体中文搜索
  - 搜索界面也可以临时开/关
- 将适配配置的添加/修改移动至网页控件适配配置中
  - 添加吸管，方便选取网页中的节点
- 优化了适配配置的错误反馈

---

## v0.16.1 - 2024-12-31

### 修复

- 修复了浏览器重启脚本不注入的问题
- 修复了控件在后台标签页无法工作的问题
  - 例如扩展在标签页 A 运行，然后你切到了标签页 B 或者切换到另一个浏览器窗口，标签页 A 中的扩展会失效。

---

## v0.16.0 - 2024-12-30

### 新增

- 针对 iframe 的优化 (#39)
  - **更新后可能需要手动修改装填配置**
  - **更新需要新的权限，更新后可能需要重新授权**
  - 重写弹幕渲染方式，不再区分视频是否嵌套于 iframe
  - 更新后，装填配置的 URL 模式一律填写主网页的地址模式（也就是地址栏里的），不再需要 iframe 的地址。如果现有的 URL 模式填写的是
    iframe 的地址，需要改成主网页的。
  - 由于不再区分 iframe，所有网站都可以使用适配配置来进行自动匹配。

### 已知的问题

- 如果网页中存在多个 frame（主网页也算一个 frame），且有多个 frame 包含视频，弹幕可能会渲染在错误的视频上（暂时没有发现这种网站）。
- 视频嵌套于 iframe 中时无法使用画中画。
- 切换标签页会导致检测不到视频/装填失败等问题。

### 其他改动

- 净化了一部分石山代码
- 各种 BUG 太多了懒得写

---

## v0.15.4 - 2024-12-20

### 新增

- 拓展内显示更新日志

### 修复

- 修复了部分导出的弹幕无法再次导入的问题 (#50)
- 修复了部分网页中拓展窗口内无法输入文字的问题 (#47)
- 修复了由于装填配置的顺序导致配置不生效的问题 (#48)

---

## v0.15.3 - 2024-11-14

### 新增

- 将弹幕设置页移植到网页弹出菜单中 (#46)

---

## v0.15.2 - 2024-10-24

### 新增

- 为部分功能添加了键盘快捷键，可在设置中更改 (#44)

---

## v0.15.1 - 2024-09-19

### 修复

- 修复了保存按钮在浅色模式下看不见的问题 (#36)

---

## v0.15.0 - 2024-09-18

### 自定义适配

- 基于 XPath 和 regex 规则的自定义网页适配，替换掉原来的方案
  - 试验功能，尚不稳定
  - 编写方法 [链接待补充]
  - **注意：** 现有的适配将被清空，需要在装填配置中重新选择
  - 暂不支持视频位于 `<iframe>` 中的情况（研究中）
  - 适配固定使用弹弹Play 搜索，之后添加对其他源的支持

### 其他

- 修复了一些关于视频检测和搜索的 Bug

---

v0.15.0 以前的更新日志请查看发布页面。
