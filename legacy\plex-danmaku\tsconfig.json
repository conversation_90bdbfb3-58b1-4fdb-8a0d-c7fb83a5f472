{"extends": "../../tsconfig.json", "compilerOptions": {"jsxImportSource": "preact", "types": [], "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@danmaku-anywhere/danmaku-engine": ["../danmaku-engine/src"], "@danmaku-anywhere/dandanplay-api": ["../dandanplay-api/src"]}}, "include": ["./src"], "references": [{"path": "./tsconfig.node.json"}, {"path": "../danmaku-engine"}, {"path": "../dandanplay-api"}]}