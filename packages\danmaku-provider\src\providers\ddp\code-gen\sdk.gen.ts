// This file is auto-generated by @hey-api/openapi-ts

import type {
  Options as ClientOptions,
  TDataShape,
  Client,
} from '@hey-api/client-fetch'
import type {
  BangumiGetShinBangumiData,
  BangumiGetShinBangumiResponse,
  BangumiGetSeasonBangumiOfAnimeData,
  BangumiGetSeasonBangumiOfAnimeResponse,
  BangumiGetSeasonsData,
  BangumiGetSeasonsResponse,
  BangumiGetQueueIntroData,
  BangumiGetQueueIntroResponse,
  BangumiGetQueueDetailsData,
  BangumiGetQueueDetailsResponse,
  BangumiGetBangumiDetailsData,
  BangumiGetBangumiDetailsResponse,
  CommentGetCommentData,
  CommentSendCommentData,
  CommentSendCommentResponse,
  CommentGetExtCommentData,
  FavoriteGetUserFavoriteData,
  FavoriteGetUserFavoriteResponse,
  FavoriteAddFavoriteData,
  FavoriteAddFavoriteResponse,
  FavoriteDeleteFavoriteData,
  FavoriteDeleteFavoriteResponse,
  HomepageGetHomepageData,
  HomepageGetHomepageResponse,
  HomepageGetBannerData,
  HomepageGetBannerResponse,
  LoginLoginData,
  LoginLoginResponse,
  LoginRenewTokenData,
  LoginRenewTokenResponse,
  MatchMatchData,
  MatchMatchResponse,
  MatchBatchMatchData,
  MatchBatchMatchResponse,
  PlayHistoryGetUserPlayHistoryData,
  PlayHistoryGetUserPlayHistoryResponse,
  PlayHistoryAddPlayHistoryData,
  PlayHistoryAddPlayHistoryResponse,
  RegisterRegisterMainUserData,
  RegisterRegisterMainUserResponse,
  RegisterResetPasswordData,
  RegisterResetPasswordResponse,
  RegisterFindMyIdData,
  RegisterFindMyIdResponse,
  RelatedDeleteRelatedData,
  RelatedDeleteRelatedResponse,
  RelatedGetRelatedData,
  RelatedGetRelatedResponse,
  RelatedAddRelatedData,
  RelatedAddRelatedResponse,
  SearchSearchAnimeData,
  SearchSearchAnimeResponse,
  SearchSearchTmdbData,
  SearchSearchTmdbResponse,
  SearchSearchEpisodesData,
  SearchSearchEpisodesResponse,
  SearchSearchAnimeByTagData,
  SearchSearchAnimeByTagResponse,
  SearchGetSearchAdvConfigData,
  SearchGetSearchAdvConfigResponse,
  SearchSearchAdvancedData,
  SearchSearchAdvancedResponse,
  UserUpdatePasswordData,
  UserUpdatePasswordResponse,
  UserUpdateProfileData,
  UserUpdateProfileResponse,
  UserUpdateUserEmailData,
  UserUpdateUserEmailResponse,
} from './types.gen'
import { client as _heyApiClient } from './client.gen'

export type Options<
  TData extends TDataShape = TDataShape,
  ThrowOnError extends boolean = boolean,
> = ClientOptions<TData, ThrowOnError> & {
  /**
   * You can provide a client instance returned by `createClient()` instead of
   * individual options. This might be also useful if you want to implement a
   * custom client.
   */
  client?: Client
}

/**
 * 获取新番列表
 * ### 接口说明
 * 此接口用于获取官方的新番列表
 *
 * ### 所需权限
 * 当未提供jwt token时，将认为是匿名用户，返回的番剧列表中`isFavorited`始终为`false`。
 * 当提供jwt token时（登录状态），返回的番剧列表中将按照当前用户对番剧关注状态设定`isFavorited`值。
 */
export const bangumiGetShinBangumi = <ThrowOnError extends boolean = false>(
  options?: Options<BangumiGetShinBangumiData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    BangumiGetShinBangumiResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/bangumi/shin',
    ...options,
  })
}

/**
 * 获取指定季度中上映的动画番剧
 * ### 接口说明
 * 此接口用于获取指定季度中上映的动画番剧列表。
 *
 * ## 参数说明
 * Url中的`year`与`month`参数需要先通过`/season/anime`接口获取。
 * 例如2018年只有1、4、7、10四个季度，如果`month`的值不为此四个数字之一将无法获取到对应季度的番剧。
 *
 * ### 所需权限
 * 当未提供jwt token时，将认为是匿名用户，返回的番剧列表中`isFavorited`始终为`false`。
 * 当提供jwt token时（登录状态），返回的番剧列表中将按照当前用户对番剧关注状态设定`isFavorited`值。
 */
export const bangumiGetSeasonBangumiOfAnime = <
  ThrowOnError extends boolean = false,
>(
  options: Options<BangumiGetSeasonBangumiOfAnimeData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<
    BangumiGetSeasonBangumiOfAnimeResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/bangumi/season/anime/{year}/{month}',
    ...options,
  })
}

/**
 * 获取动画类型番剧季度的列表
 */
export const bangumiGetSeasons = <ThrowOnError extends boolean = false>(
  options?: Options<BangumiGetSeasonsData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    BangumiGetSeasonsResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/bangumi/season/anime',
    ...options,
  })
}

/**
 * 获取近期未看番剧的列表
 * ### 接口说明
 * 此接口用户获取用户近期关注但未看/未看完的番剧的列表。
 *
 * ### 权限需求
 * 此接口需要登录状态才可调用。
 */
export const bangumiGetQueueIntro = <ThrowOnError extends boolean = false>(
  options?: Options<BangumiGetQueueIntroData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    BangumiGetQueueIntroResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/bangumi/queue/intro',
    ...options,
  })
}

/**
 * 获取完整版未看番剧的列表
 * ### 接口说明
 * 此接口用户获取用户完整的未看完的番剧的列表。
 *
 * ### 权限需求
 * 此接口需要登录状态才可调用。
 */
export const bangumiGetQueueDetails = <ThrowOnError extends boolean = false>(
  options?: Options<BangumiGetQueueDetailsData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    BangumiGetQueueDetailsResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/bangumi/queue/details',
    ...options,
  })
}

/**
 * 获取番剧详情
 * ### 接口说明
 * 此接口用于获取指定编号的作品的详细数据，包括简介、评分、详细剧集等。
 *
 * ### 所需权限
 * 此接口无需登录状态即可调用。当提供了token时，返回的剧集列表中将包含当前用户的上次播放时间。
 */
export const bangumiGetBangumiDetails = <ThrowOnError extends boolean = false>(
  options: Options<BangumiGetBangumiDetailsData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<
    BangumiGetBangumiDetailsResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/bangumi/{bangumiId}',
    ...options,
  })
}

/**
 * 获取指定弹幕库的所有弹幕
 * ### 接口说明
 * 此接口用于获取弹弹play服务器上指定弹幕库的弹幕。
 *
 * ### withRelated 参数
 * 当`withRelated`参数为`true`时，接口将会返回此弹幕库对应的所有第三方关联网址的弹幕。相当于同时调用了 /related 和 /extcomment 接口。
 * 如果您没有解析第三方弹幕的能力，推荐使用此参数获取整合后的弹幕。
 *
 * ### 接口跳转
 * 在调用此接口时，将会跳转到弹幕加速服务上获取弹幕。返回的状态码为302，Location头部包含了跳转的地址。
 *
 * ### 返回值
 * 字段`p`的说明：格式为`出现时间,模式,颜色,用户ID`，各个值之间使用英文逗号分隔
 * * 弹幕出现时间：格式为 0.00，单位为秒，精确到小数点后两位，例如12.34、445.6、789.01
 * * 弹幕模式：1-普通弹幕，4-底部弹幕，5-顶部弹幕
 * * 颜色：32位整数表示的颜色，算法为 Rx256x256+Gx256+B，R/G/B的范围应是0-255
 * * 用户ID：字符串形式表示的用户ID，通常为数字，不会包含特殊字符
 */
export const commentGetComment = <ThrowOnError extends boolean = false>(
  options: Options<CommentGetCommentData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/comment/{episodeId}',
    ...options,
  })
}

/**
 * 向指定的弹幕库发送弹幕
 * ### 接口说明
 * 此接口用于客户端向弹弹play服务器上的指定弹幕库发送弹幕。
 *
 * ### 权限需求
 * 此接口需要用户登录后才可使用
 */
export const commentSendComment = <ThrowOnError extends boolean = false>(
  options: Options<CommentSendCommentData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    CommentSendCommentResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/comment/{episodeId}',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 获取指定第三方url的弹幕
 * ### 接口说明
 * 此接口用于获取第三方弹幕站（如A/B/C站）上指定网址对应的弹幕。
 *
 * ### 注意事项
 * * 因为网址中可能包含特殊字符，`url`参数需要先进行UrlEncode编码
 * * 获取到的弹幕并非最新，而是服务器上的缓存。所以此接口有可能获取到已被删除视频的弹幕。
 * * 当网址不支持解析或缓存不存在时，将返回0条弹幕。
 *
 * ### 接口跳转说明
 * 在调用此接口时，将会跳转到弹幕加速服务上获取弹幕。返回的状态码为302，Location头部包含了跳转的地址。
 *
 * ### 返回值
 * 字段`p`的说明：格式为`出现时间,模式,颜色,用户ID`，各个值之间使用英文逗号分隔
 * * 弹幕出现时间：格式为 0.00，单位为秒，精确到小数点后两位，例如12.34、445.6、789.01
 * * 弹幕模式：1-普通弹幕，4-底部弹幕，5-顶部弹幕
 * * 颜色：32位整数表示的颜色，算法为 Rx256x256+Gx256+B，R/G/B的范围应是0-255
 * * 用户ID：字符串形式表示的用户ID
 */
export const commentGetExtComment = <ThrowOnError extends boolean = false>(
  options?: Options<CommentGetExtCommentData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>(
    {
      security: [
        {
          name: 'X-AppId',
          type: 'apiKey',
        },
        {
          name: 'X-AppSecret',
          type: 'apiKey',
        },
      ],
      url: '/api/v2/extcomment',
      ...options,
    }
  )
}

/**
 * 获取当前用户关注的所有动画作品
 * ### 接口说明
 * 此接口用于获取用户当前关注的所有动画作品信息
 *
 * ### 权限需求
 * 此接口需要登录状态才能调用
 */
export const favoriteGetUserFavorite = <ThrowOnError extends boolean = false>(
  options?: Options<FavoriteGetUserFavoriteData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    FavoriteGetUserFavoriteResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/favorite',
    ...options,
  })
}

/**
 * 添加关注
 * ### 接口说明
 * 此接口用于为用户增加关注某一部作品。
 *
 * ### 权限需求
 * 此接口需要登录状态才能调用，同时应用应拥有添加关注的权限。
 */
export const favoriteAddFavorite = <ThrowOnError extends boolean = false>(
  options: Options<FavoriteAddFavoriteData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    FavoriteAddFavoriteResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/favorite',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 取消关注
 * ### 接口说明
 * 此接口用于为用户取消关注某一部作品。
 *
 * ### 权限需求
 * 此接口需要登录状态才能调用，同时应用应拥有取消关注的权限。
 */
export const favoriteDeleteFavorite = <ThrowOnError extends boolean = false>(
  options: Options<FavoriteDeleteFavoriteData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<
    FavoriteDeleteFavoriteResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/favorite/{animeId}',
    ...options,
  })
}

/**
 * 获取整合后的首页数据
 * ### 接口说明
 * 此接口用于一次性获取系统公告、未看剧集列表、当季新番列表、热门种子等接口的数据，并合并为同一个文档进行返回。
 *
 * ### 权限需求
 * 当未提供jwt token时，将认为是匿名用户，返回的番剧列表中`isFavorited`始终为`false`。
 * 当提供jwt token时（登录状态），返回的番剧列表中将按照当前用户对番剧关注状态设定`isFavorited`值。
 */
export const homepageGetHomepage = <ThrowOnError extends boolean = false>(
  options?: Options<HomepageGetHomepageData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    HomepageGetHomepageResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/homepage',
    ...options,
  })
}

/**
 * 获取系统公告
 */
export const homepageGetBanner = <ThrowOnError extends boolean = false>(
  options?: Options<HomepageGetBannerData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    HomepageGetBannerResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/homepage/banner',
    ...options,
  })
}

/**
 * 使用用户名密码登录
 * ### 接口说明
 * 通过此接口可以使用用户名/密码获取到后续接口需要的JWT Token。
 * 调用此接口需要有应用的AppId与AppSecret，您可以联系弹弹play开发方申请。
 *
 * ### Hash计算方法
 * Hash属性的计算方法为，将登录请求中 `appId` `password` `unixTimestamp` `userName` 属性的值以及您应用的 `AppSecret` 密钥的值依次拼接起来，
 * 计算出32位MD5（不区分大小写）。举例来说，`appId`为`dandanplay`，AppSecret为`FFFFF`，用户名为`test1`，密码为`test2`，
 * 那么计算方法将会是 `hash=MD5(dandanplaytest2666666666test1FFFFF)`。
 *
 * ### 错误代码
 * 当调用接口发生错误时，例如参数不完整、验证错误、登录失败，`success`属性值将为`false`，`errorCode`代码将不为`0`，
 * 同时`errorMessage`属性将包含错误的描述信息 。
 */
export const loginLogin = <ThrowOnError extends boolean = false>(
  options: Options<LoginLoginData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    LoginLoginResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/login',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 延长已有Token的有效时间
 * ### 接口说明
 * 默认情况下Token的有效期为21天，此接口用于在此期间延长一个有效的JWT Token的有效时间。
 *
 * ### 权限需求
 * 此接口需要登录后才可使用（请求中包含Authorization头）
 *
 * ### 返回值说明
 * 调用此接口后相当于重新使用当前用户的信息进行重新登录，将会返回最新的用户信息（包括已延长有效期的JWT Token）。
 * 如果应用或用户的状态异常，将会返回相应的错误代码。
 */
export const loginRenewToken = <ThrowOnError extends boolean = false>(
  options?: Options<LoginRenewTokenData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    LoginRenewTokenResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/login/renew',
    ...options,
  })
}

/**
 * 使用指定的文件名、Hash、文件长度信息寻找文件可能对应的节目信息。
 * ### 接口说明
 * 此接口用于当用户打开某视频文件时，可以通过文件名称、Hash等信息查找此视频可能对应的节目信息。
 *
 * 此接口首先会使用Hash信息进行搜寻，如果有相应的记录，会返回“精确关联”的结果（即`isMatched`属性为`true`，此时列表中只包含一个搜索结果）。
 *
 * 如果Hash信息匹配失败，则会继续通过文件名进行模糊搜寻。
 *
 *
 * ### 返回值说明
 * 一个包含节目信息的列表，节目在列表中排名越靠前，这个节目越有可能是视频文件的内容。
 * 当列表中只有一个节目时（`isMatched`属性为`true`），视为“精确关联” —— 说明此视频已被人工关联了某一节目。客户端应自动选择这个唯一的结果，不必再让用户做出选择。
 */
export const matchMatch = <ThrowOnError extends boolean = false>(
  options: Options<MatchMatchData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    MatchMatchResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/match',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 使用指定的文件信息批量匹配节目信息
 * ### 接口说明
 * 此接口用于批量匹配（参考`/match`接口），可以通过Hash、文件名称等信息查找多个视频对应的节目信息。
 * 每次批量匹配提供的文件信息不能多于`32`个，文件信息中不能有重复项。
 * 此接口只会返回“精确关联”的结果，如果文件未能成功匹配上一个弹幕库，对应匹配结果的`success`将为`false`。
 *
 * ### 返回值说明
 * 一个包含匹配结果的列表，将与请求中的文件信息一一对应。例如请求中包含了20个文件信息，返回结果的列表中也将包含20个匹配结果。
 *
 * 如果某个文件匹配成功，对应结果的`success`属性将为`true`。如果某文件未匹配成功，或是某个请求未通过验证，对应结果的`success`属性将为`false`。
 */
export const matchBatchMatch = <ThrowOnError extends boolean = false>(
  options: Options<MatchBatchMatchData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    MatchBatchMatchResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/match/batch',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 获取用户播放历史
 * ### 接口说明
 * 此接口用于获取用户的播放历史（作品+剧集）。只能获取到用户已关注作品的播放历史。
 *
 * ### 权限需求
 * 此接口需要登录状态才可以调用。
 *
 * ### 开始结束日期参数说明
 * 开始日期不能晚于结束日期；
 * 开始日期与结束日期不能相差大于一年（最多查询一年的数据）；
 * 当没有提供`toDate`参数时，默认将使用当前日期；
 * 当没有提供`fromDate`参数时，默认将使用`toDate`减去三个月的日期。
 */
export const playHistoryGetUserPlayHistory = <
  ThrowOnError extends boolean = false,
>(
  options?: Options<PlayHistoryGetUserPlayHistoryData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    PlayHistoryGetUserPlayHistoryResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/playhistory',
    ...options,
  })
}

/**
 * 增加播放历史记录和评分
 * ### 接口说明
 * 此接口用于提交用户的播放历史数据，同时可以更新用户对某剧集的评分。
 *
 * ### 权限需求
 * 此接口需要登录权限才可以调用。
 *
 * ### 参数限制说明
 * 接口支持单个或批量增加历史数据。
 *
 * 提交的请求中，如果`episodeIdList`数组只包含一条数据，则`addToFavorite`参数（关注此作品）和`rating`参数（更新评分）可以生效。
 * 如果`episodeIdList`数组包含不止一条数据，则会忽略`addToFavorite`和`rating`参数。
 *
 * 在批量添加历史记录时，`episodeIdList`数组最多只能包含100条数据，而且其中的episodeId必须全部属于同一部作品。
 */
export const playHistoryAddPlayHistory = <ThrowOnError extends boolean = false>(
  options: Options<PlayHistoryAddPlayHistoryData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    PlayHistoryAddPlayHistoryResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/playhistory',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 注册新的弹弹play用户
 * ### 接口说明
 * 通过此接口可以注册新的弹弹play用户，注册成功后将返回登录结果。
 * 调用此接口需要有应用的AppId与AppSecret，您可以联系弹弹play开发方申请。
 *
 * ### Hash计算方法
 * Hash属性的计算方法为，将登录请求中 `appId` `email` `password` `screenName` `unixTimestamp` `userName` 属性的值加上您应用的 `AppSecret` 密钥的值按顺序拼接起来，
 * 计算出32位MD5（不区分大小写）。举例来说，`appId`为`dandanplay`，AppSecret为`FFFFF`，用户名为`test1`，密码为`test2`，邮箱为`<EMAIL>`，昵称为`弹弹`
 * 那么计算方法将会是 `hash=MD5(dandanplaytest3@example.comtest2弹弹666666666test1FFFFF)`。
 *
 * ### 错误代码
 * 当调用接口发生错误时，例如参数不完整、验证错误、登录失败，`success`属性值将为`false`，`errorCode`代码将不为`0`，
 * 同时`errorMessage`属性将包含错误的描述信息 。
 */
export const registerRegisterMainUser = <ThrowOnError extends boolean = false>(
  options: Options<RegisterRegisterMainUserData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    RegisterRegisterMainUserResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/register',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 重置用户密码
 * ### 接口说明
 * 通过此接口可以重置一个用户的密码至随机密码，重置成功后新的随机密码将会发送到对应的邮箱中。
 * 调用此接口需要有应用的AppId与AppSecret，您可以联系弹弹play开发方申请。
 *
 * ### 请求说明
 * 请求参数中`userName`和`email`必须和注册时的信息完全一致，方能成功重置。
 * 重置密码的请求每2分钟只能发送一次，否则会返回错误信息。
 *
 * ### Hash计算方法
 * Hash属性的计算方法为，将登录请求中 `appId` `email` `unixTimestamp` `userName` 属性的值加上您应用的 `AppSecret` 密钥的值按顺序拼接起来，
 * 计算出32位MD5（不区分大小写）。举例来说，`appId`为`dandanplay`，AppSecret为`FFFFF`，用户名为`test1`，邮箱为`<EMAIL>`，
 * 那么计算方法将会是 `hash=MD5(dandanplaytest3@example.com666666666test1FFFFF)`。
 *
 * ### 错误代码
 * 当调用接口发生错误时，例如参数不完整、验证错误、登录失败，`success`属性值将为`false`，`errorCode`代码将不为`0`，
 * 同时`errorMessage`属性将包含错误的描述信息 。
 */
export const registerResetPassword = <ThrowOnError extends boolean = false>(
  options: Options<RegisterResetPasswordData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    RegisterResetPasswordResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/register/resetpassword',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 查找邮箱对应的用户名
 * ### 接口说明
 * 通过此接口可以查找一个指定邮箱对应的用户名，查找结果将会发送到对应的邮箱中。
 * 调用此接口需要有应用的AppId与AppSecret，您可以联系弹弹play开发方申请。
 *
 * ### 请求说明
 * 请求参数中`email`必须和注册时的信息完全一致，方能查找成功。
 * 查找用户名的请求每`10`分钟只能发送一次，否则会返回错误信息。
 *
 * ### Hash计算方法
 * Hash属性的计算方法为，将登录请求中 `appId` `email` `unixTimestamp` 属性的值加上您应用的 `AppSecret` 密钥的值按顺序拼接起来，
 * 计算出32位MD5（不区分大小写）。举例来说，`appId`为`dandanplay`，AppSecret为`FFFFF`，邮箱为`<EMAIL>`，
 * 那么计算方法将会是 `hash=MD5(dandanplaytest3@example.com666666666FFFFF)`。
 *
 * ### 错误代码
 * 当调用接口发生错误时，例如参数不完整、验证错误、登录失败，`success`属性值将为`false`，`errorCode`代码将不为`0`，
 * 同时`errorMessage`属性将包含错误的描述信息 。
 */
export const registerFindMyId = <ThrowOnError extends boolean = false>(
  options: Options<RegisterFindMyIdData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    RegisterFindMyIdResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/register/findmyid',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 向某个第三方弹幕源关联数据投反对票
 * ### 接口说明
 * 此接口用于对某个“弹幕库-第三方弹幕源”的关联数据投出反对票。
 *
 * ### 权限需求
 * 此接口需要登录状态才可以调用。
 *
 * ### 提交说明
 * 提交后并不意味着数据会被直接接受。
 * 当用户对某个已被服务器接受的“弹幕库-第三方弹幕源”的关联投出足够多比例和数量的反对票时，
 * 服务器才会从弹幕库中删除对应的第三方弹幕源关联。
 */
export const relatedDeleteRelated = <ThrowOnError extends boolean = false>(
  options: Options<RelatedDeleteRelatedData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<
    RelatedDeleteRelatedResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/related/{episodeId}',
    ...options,
  })
}

/**
 * 获取指定弹幕库关联的所有第三方弹幕源信息
 * ### 接口说明
 * 此接口用于在弹弹play官方弹幕库中弹幕不足的时候，获取此弹幕库对应的其他网站的播放地址，用以补充弹幕数量，提高观看体验。
 *
 * ### 第三方弹幕源
 * 第三方弹幕源指的是除弹弹play自己外其他弹幕提供网站。常见的Acfun、BiliBili、Tucao等。
 * 应用可以使用 *extcomment* 接口获取来自第三方网站的弹幕，不必自行编写解析代码。
 */
export const relatedGetRelated = <ThrowOnError extends boolean = false>(
  options: Options<RelatedGetRelatedData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<
    RelatedGetRelatedResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/related/{episodeId}',
    ...options,
  })
}

/**
 * 提交第三方关联弹幕源数据（投赞成票）
 * ### 接口说明
 * 此接口用于提交“弹幕库-第三方弹幕源”的关联数据，或是对已有的关联数据投出赞成票。
 *
 * ### 权限需求
 * 此接口需要登录状态才可以调用。
 *
 * ### 提交说明
 * 提交后并不意味着数据会被直接接受。
 * 当用户对某个“弹幕库-第三方弹幕源”的关联投出足够数量和比例的赞成票时，服务器才会接受对应的关联。
 */
export const relatedAddRelated = <ThrowOnError extends boolean = false>(
  options: Options<RelatedAddRelatedData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    RelatedAddRelatedResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/related/{episodeId}',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 根据关键词搜索作品
 * ### 接口说明
 * 根据用户提供的关键词，在弹弹play数据库中搜索对应的作品信息，搜索结果中不包含剧集信息。
 *
 * ### 权限需求
 * 不需要登录状态即可使用
 *
 * ### 关键词说明
 * * 关键词长度至少为`2`。
 * * 关键词中的空格将被认定为 AND 条件，其他字符将被作为原始字符去搜索。
 * * 可以通过中文、日文、罗马音、英文等条件对作品的别名进行搜索，繁体中文关键词将被统一为简体中文。
 */
export const searchSearchAnime = <ThrowOnError extends boolean = false>(
  options?: Options<SearchSearchAnimeData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    SearchSearchAnimeResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/search/anime',
    ...options,
  })
}

/**
 * 根据关键词搜索TMDB中的作品
 * ### 接口说明
 * 根据用户提供的关键词，在TMDB数据库中搜索作品，搜索结果中不包含剧集信息。
 *
 * ### 权限需求
 * 不需要登录状态即可使用
 *
 * ### 关键词说明
 * * 关键词长度至少为`2`。
 * * 可以通过中文、日文、罗马音、英文等条件对作品的别名进行搜索。
 *
 * ### 返回结果
 * 返回结果中将包含TMDB电视剧和电影的搜索结果。电视剧结果排列在前，电影将排列在后。
 */
export const searchSearchTmdb = <ThrowOnError extends boolean = false>(
  options?: Options<SearchSearchTmdbData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    SearchSearchTmdbResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/search/tmdb',
    ...options,
  })
}

/**
 * 根据关键词搜索所有匹配的剧集信息
 * ### 接口说明
 * 此接口用于根据关键词搜索所有匹配的剧集信息。
 * 当自动匹配失败或结果不理想时可以调用此接口，让用户手动通过关键词搜索到作品。
 *
 * ### 参数说明
 * - anime：作品标题。支持通过中文、日语（含罗马音）、英语搜索，至少为2个字符。
 * - tmdbId：使用 TMDB ID 搜索作品，如果指定此参数，将仅返回此 TMDB ID 的关联作品（可能有多个）。
 * - episode：剧集标题，默认为空。当参数为纯数字时，将仅保留指定集数的结果。当参数值为“movie”时，将仅保留剧场版的结果。其他情况下，将仅保留剧集标题中包含这个文字的结果，不建议使用。
 *
 * 必须提供`anime`和`tmdbId`中至少一个参数。
 * 当同时提供`anime`和`tmdbId`参数时，会先尝试使用`anime`参数进行搜索，之后在搜索结果中匹配`tmdbId`的剧集。
 *
 * ### 参数注意事项
 * * 参数可以包含空格，但空格将作为查询字符串的一部分而不是传统的“OR”查询。
 * * 未提供`episode`参数的情况下，如果`anime`参数中包含空格，且空格后为数字（如“EVA 10”），此数字将被认定为是`episode`参数。
 * * 如果参数中包含特殊字符，需要经过Url编码后才能传递。
 *
 * ### 返回值说明
 * 接口将返回包含节目信息的列表，当结果集过大时，`hasMore`属性为`true`，这时客户端应该提示用户填写更详细的信息以缩小搜索范围。
 */
export const searchSearchEpisodes = <ThrowOnError extends boolean = false>(
  options?: Options<SearchSearchEpisodesData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    SearchSearchEpisodesResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/search/episodes',
    ...options,
  })
}

/**
 * 根据标签搜索最匹配的作品
 * ### 接口说明
 * 根据用户提供的标签列表搜索到对应的作品信息，搜索结果中不包含剧集信息。
 *
 * ### 权限需求
 * 不需要登录状态即可使用。返回中的`isFavorited`属性目前都为`false`。
 *
 * ### 返回值
 * 将返回根据提供的标签列表最匹配的作品列表。
 *
 * ### 参数说明
 * `tags`参数必须为标签ID使用逗号连接起来，例如想查询拥有 15、20、66、108 标签的作品，需要传入 `15,20,66,108`。
 * 参数不能包含除数字与逗号之外其他的字符。
 */
export const searchSearchAnimeByTag = <ThrowOnError extends boolean = false>(
  options?: Options<SearchSearchAnimeByTagData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    SearchSearchAnimeByTagResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/search/tag',
    ...options,
  })
}

/**
 * 获取高级搜索默认配置
 * ### 接口说明
 * 获取高级搜索功能所需的配置项，用于初始化客户端搜索界面。例如类别、标签等。
 *
 * ### 权限需求
 * 不需要登录状态即可使用。
 */
export const searchGetSearchAdvConfig = <ThrowOnError extends boolean = false>(
  options?: Options<SearchGetSearchAdvConfigData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    SearchGetSearchAdvConfigResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/search/adv/config',
    ...options,
  })
}

/**
 * 高级搜索
 */
export const searchSearchAdvanced = <ThrowOnError extends boolean = false>(
  options?: Options<SearchSearchAdvancedData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<
    SearchSearchAdvancedResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/search/adv',
    ...options,
  })
}

/**
 * 为已登录用户修改密码
 * ### 接口说明
 * 此接口用于为已经登录的用户修改当前的登录密码。
 *
 * ### 权限需求
 * 此接口需要登录后才可使用（请求中包含Authorization头）
 */
export const userUpdatePassword = <ThrowOnError extends boolean = false>(
  options: Options<UserUpdatePasswordData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    UserUpdatePasswordResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/user/password',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 修改用户资料（昵称、头像等）
 * ### 接口说明
 * 此接口用于为已经登录的用户修改当前的基本资料（如昵称、头像）。
 * 当提供`screenName`时才更新昵称，提供`profileImageBase64`时才更新头像图片，否则不会产生变化。
 *
 * ### 更新头像图片
 * 头像图片需要转换成base64编码后放入`profileImageBase64`字段中。此字段长度不能超过1MB。
 * 上传的图片将保留长宽比，转换为边长最长600px的长方形，并存储为jpg格式。
 *
 * ### 权限需求
 * 此接口需要登录后才可使用（请求中包含Authorization头）
 */
export const userUpdateProfile = <ThrowOnError extends boolean = false>(
  options: Options<UserUpdateProfileData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    UserUpdateProfileResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/user/profile',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}

/**
 * 为已登录用户修改关联邮箱
 * ### 接口说明
 * 此接口用于为已经登录的用户修改当前账号关联的邮箱地址。
 *
 * ### 权限需求
 * 此接口需要登录后才可使用（请求中包含Authorization头）
 */
export const userUpdateUserEmail = <ThrowOnError extends boolean = false>(
  options: Options<UserUpdateUserEmailData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<
    UserUpdateUserEmailResponse,
    unknown,
    ThrowOnError
  >({
    security: [
      {
        name: 'X-AppId',
        type: 'apiKey',
      },
      {
        name: 'X-AppSecret',
        type: 'apiKey',
      },
    ],
    url: '/api/v2/user/email',
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  })
}
