// This file is auto-generated by @hey-api/openapi-ts

import { z } from 'zod'

export const zBangumiListResponse = z
  .object({
    errorCode: z.number().int().optional(),
    success: z.boolean().optional(),
    errorMessage: z.union([z.string(), z.null()]).optional(),
  })
  .merge(
    z.object({
      bangumiList: z
        .union([
          z.array(
            z.object({
              animeId: z.number().int().optional(),
              bangumiId: z.union([z.string(), z.null()]).optional(),
              animeTitle: z.union([z.string(), z.null()]).optional(),
              imageUrl: z.union([z.string(), z.null()]).optional(),
              searchKeyword: z.union([z.string(), z.null()]).optional(),
              isOnAir: z.boolean().optional(),
              airDay: z.number().int().optional(),
              isFavorited: z.boolean().optional(),
              isRestricted: z.boolean().optional(),
              rating: z.number().optional(),
            })
          ),
          z.null(),
        ])
        .optional(),
    })
  )

export const zBangumiIntro = z.object({
  animeId: z.number().int().optional(),
  bangumiId: z.union([z.string(), z.null()]).optional(),
  animeTitle: z.union([z.string(), z.null()]).optional(),
  imageUrl: z.union([z.string(), z.null()]).optional(),
  searchKeyword: z.union([z.string(), z.null()]).optional(),
  isOnAir: z.boolean().optional(),
  airDay: z.number().int().optional(),
  isFavorited: z.boolean().optional(),
  isRestricted: z.boolean().optional(),
  rating: z.number().optional(),
})

export const zResponseBase = z.object({
  errorCode: z.number().int().optional(),
  success: z.boolean().optional(),
  errorMessage: z.union([z.string(), z.null()]).optional(),
})

export const zBangumiSeasonListResponse = zResponseBase.merge(
  z.object({
    seasons: z
      .union([
        z.array(
          z.object({
            year: z.number().int().optional(),
            month: z.number().int().optional(),
            seasonName: z.union([z.string(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zBangumiSeason = z.object({
  year: z.number().int().optional(),
  month: z.number().int().optional(),
  seasonName: z.union([z.string(), z.null()]).optional(),
})

export const zBangumiQueueIntroResponseV2 = zResponseBase.merge(
  z.object({
    hasMore: z.boolean().optional(),
    bangumiList: z
      .union([
        z.array(
          z.object({
            animeId: z.number().int().optional(),
            animeTitle: z.union([z.string(), z.null()]).optional(),
            episodeTitle: z.union([z.string(), z.null()]).optional(),
            airDate: z.union([z.string().datetime(), z.null()]).optional(),
            imageUrl: z.union([z.string(), z.null()]).optional(),
            description: z.union([z.string(), z.null()]).optional(),
            isOnAir: z.boolean().optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zBangumiQueueIntroV2 = z.object({
  animeId: z.number().int().optional(),
  animeTitle: z.union([z.string(), z.null()]).optional(),
  episodeTitle: z.union([z.string(), z.null()]).optional(),
  airDate: z.union([z.string().datetime(), z.null()]).optional(),
  imageUrl: z.union([z.string(), z.null()]).optional(),
  description: z.union([z.string(), z.null()]).optional(),
  isOnAir: z.boolean().optional(),
})

export const zBangumiQueueDetailsResponseV2 = zResponseBase.merge(
  z.object({
    bangumiList: z
      .union([
        z.array(
          z.object({
            animeId: z.number().int().optional(),
            animeTitle: z.union([z.string(), z.null()]).optional(),
            isOnAir: z.boolean().optional(),
            imageUrl: z.union([z.string(), z.null()]).optional(),
            searchKeyword: z.union([z.string(), z.null()]).optional(),
            lastWatched: z.union([z.string().datetime(), z.null()]).optional(),
            episodes: z
              .union([
                z.array(
                  z.object({
                    episodeId: z.coerce.bigint().optional(),
                    episodeTitle: z.union([z.string(), z.null()]).optional(),
                    airDate: z
                      .union([z.string().datetime(), z.null()])
                      .optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    unwatchedBangumiList: z
      .union([
        z.array(
          z.object({
            animeId: z.number().int().optional(),
            animeTitle: z.union([z.string(), z.null()]).optional(),
            isOnAir: z.boolean().optional(),
            imageUrl: z.union([z.string(), z.null()]).optional(),
            searchKeyword: z.union([z.string(), z.null()]).optional(),
            lastWatched: z.union([z.string().datetime(), z.null()]).optional(),
            episodes: z
              .union([
                z.array(
                  z.object({
                    episodeId: z.coerce.bigint().optional(),
                    episodeTitle: z.union([z.string(), z.null()]).optional(),
                    airDate: z
                      .union([z.string().datetime(), z.null()])
                      .optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zBangumiQueueDetailsV2 = z.object({
  animeId: z.number().int().optional(),
  animeTitle: z.union([z.string(), z.null()]).optional(),
  isOnAir: z.boolean().optional(),
  imageUrl: z.union([z.string(), z.null()]).optional(),
  searchKeyword: z.union([z.string(), z.null()]).optional(),
  lastWatched: z.union([z.string().datetime(), z.null()]).optional(),
  episodes: z
    .union([
      z.array(
        z.object({
          episodeId: z.coerce.bigint().optional(),
          episodeTitle: z.union([z.string(), z.null()]).optional(),
          airDate: z.union([z.string().datetime(), z.null()]).optional(),
        })
      ),
      z.null(),
    ])
    .optional(),
})

export const zBangumiQueueEpisodeV2 = z.object({
  episodeId: z.coerce.bigint().optional(),
  episodeTitle: z.union([z.string(), z.null()]).optional(),
  airDate: z.union([z.string().datetime(), z.null()]).optional(),
})

export const zBangumiDetailsResponse = zResponseBase.merge(
  z.object({
    bangumi: z
      .union([
        zBangumiIntro.merge(
          z.object({
            type: z
              .enum([
                'tvseries',
                'tvspecial',
                'ova',
                'movie',
                'musicvideo',
                'web',
                'other',
                'jpmovie',
                'jpdrama',
                'unknown',
                'tmdbtv',
                'tmdbmovie',
              ])
              .optional(),
            typeDescription: z.union([z.string(), z.null()]).optional(),
            titles: z
              .union([
                z.array(
                  z.object({
                    language: z.union([z.string(), z.null()]).optional(),
                    title: z.union([z.string(), z.null()]).optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
            seasons: z
              .union([
                z.array(
                  z.object({
                    id: z.union([z.string(), z.null()]).optional(),
                    airDate: z
                      .union([z.string().datetime(), z.null()])
                      .optional(),
                    name: z.union([z.string(), z.null()]).optional(),
                    episodeCount: z.number().int().optional(),
                    summary: z.union([z.string(), z.null()]).optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
            episodes: z
              .union([
                z.array(
                  z.object({
                    seasonId: z.union([z.string(), z.null()]).optional(),
                    episodeId: z.coerce.bigint().optional(),
                    episodeTitle: z.union([z.string(), z.null()]).optional(),
                    episodeNumber: z.union([z.string(), z.null()]).optional(),
                    lastWatched: z
                      .union([z.string().datetime(), z.null()])
                      .optional(),
                    airDate: z
                      .union([z.string().datetime(), z.null()])
                      .optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
            summary: z.union([z.string(), z.null()]).optional(),
            metadata: z.union([z.array(z.string()), z.null()]).optional(),
            bangumiUrl: z.union([z.string(), z.null()]).optional(),
            userRating: z.number().int().optional(),
            favoriteStatus: z
              .union([z.enum(['favorited', 'finished', 'abandoned']), z.null()])
              .optional(),
            comment: z.union([z.string(), z.null()]).optional(),
            ratingDetails: z.union([z.object({}), z.null()]).optional(),
            relateds: z.union([z.array(zBangumiIntro), z.null()]).optional(),
            similars: z.union([z.array(zBangumiIntro), z.null()]).optional(),
            tags: z
              .union([
                z.array(
                  z.object({
                    id: z.number().int().optional(),
                    name: z.union([z.string(), z.null()]).optional(),
                    count: z.number().int().optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
            onlineDatabases: z
              .union([
                z.array(
                  z.object({
                    name: z.union([z.string(), z.null()]).optional(),
                    url: z.union([z.string(), z.null()]).optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
            trailers: z
              .union([
                z.array(
                  z.object({
                    id: z.number().int().optional(),
                    url: z.union([z.string(), z.null()]).optional(),
                    title: z.union([z.string(), z.null()]).optional(),
                    imageUrl: z.union([z.string(), z.null()]).optional(),
                    date: z.string().datetime().optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zBangumiDetails = zBangumiIntro.merge(
  z.object({
    type: z
      .enum([
        'tvseries',
        'tvspecial',
        'ova',
        'movie',
        'musicvideo',
        'web',
        'other',
        'jpmovie',
        'jpdrama',
        'unknown',
        'tmdbtv',
        'tmdbmovie',
      ])
      .optional(),
    typeDescription: z.union([z.string(), z.null()]).optional(),
    titles: z
      .union([
        z.array(
          z.object({
            language: z.union([z.string(), z.null()]).optional(),
            title: z.union([z.string(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    seasons: z
      .union([
        z.array(
          z.object({
            id: z.union([z.string(), z.null()]).optional(),
            airDate: z.union([z.string().datetime(), z.null()]).optional(),
            name: z.union([z.string(), z.null()]).optional(),
            episodeCount: z.number().int().optional(),
            summary: z.union([z.string(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    episodes: z
      .union([
        z.array(
          z.object({
            seasonId: z.union([z.string(), z.null()]).optional(),
            episodeId: z.coerce.bigint().optional(),
            episodeTitle: z.union([z.string(), z.null()]).optional(),
            episodeNumber: z.union([z.string(), z.null()]).optional(),
            lastWatched: z.union([z.string().datetime(), z.null()]).optional(),
            airDate: z.union([z.string().datetime(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    summary: z.union([z.string(), z.null()]).optional(),
    metadata: z.union([z.array(z.string()), z.null()]).optional(),
    bangumiUrl: z.union([z.string(), z.null()]).optional(),
    userRating: z.number().int().optional(),
    favoriteStatus: z
      .union([z.enum(['favorited', 'finished', 'abandoned']), z.null()])
      .optional(),
    comment: z.union([z.string(), z.null()]).optional(),
    ratingDetails: z.union([z.object({}), z.null()]).optional(),
    relateds: z.union([z.array(zBangumiIntro), z.null()]).optional(),
    similars: z.union([z.array(zBangumiIntro), z.null()]).optional(),
    tags: z
      .union([
        z.array(
          z.object({
            id: z.number().int().optional(),
            name: z.union([z.string(), z.null()]).optional(),
            count: z.number().int().optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    onlineDatabases: z
      .union([
        z.array(
          z.object({
            name: z.union([z.string(), z.null()]).optional(),
            url: z.union([z.string(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    trailers: z
      .union([
        z.array(
          z.object({
            id: z.number().int().optional(),
            url: z.union([z.string(), z.null()]).optional(),
            title: z.union([z.string(), z.null()]).optional(),
            imageUrl: z.union([z.string(), z.null()]).optional(),
            date: z.string().datetime().optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zAnimeType = z.enum([
  'tvseries',
  'tvspecial',
  'ova',
  'movie',
  'musicvideo',
  'web',
  'other',
  'jpmovie',
  'jpdrama',
  'unknown',
  'tmdbtv',
  'tmdbmovie',
])

export const zBangumiTitle = z.object({
  language: z.union([z.string(), z.null()]).optional(),
  title: z.union([z.string(), z.null()]).optional(),
})

export const zBangumiEpisodeSeason = z.object({
  id: z.union([z.string(), z.null()]).optional(),
  airDate: z.union([z.string().datetime(), z.null()]).optional(),
  name: z.union([z.string(), z.null()]).optional(),
  episodeCount: z.number().int().optional(),
  summary: z.union([z.string(), z.null()]).optional(),
})

export const zBangumiEpisode = z.object({
  seasonId: z.union([z.string(), z.null()]).optional(),
  episodeId: z.coerce.bigint().optional(),
  episodeTitle: z.union([z.string(), z.null()]).optional(),
  episodeNumber: z.union([z.string(), z.null()]).optional(),
  lastWatched: z.union([z.string().datetime(), z.null()]).optional(),
  airDate: z.union([z.string().datetime(), z.null()]).optional(),
})

export const zFavoriteStatus = z.enum(['favorited', 'finished', 'abandoned'])

export const zBangumiTag = z.object({
  id: z.number().int().optional(),
  name: z.union([z.string(), z.null()]).optional(),
  count: z.number().int().optional(),
})

export const zBangumiOnlineDatabase = z.object({
  name: z.union([z.string(), z.null()]).optional(),
  url: z.union([z.string(), z.null()]).optional(),
})

export const zBangumiTrailer = z.object({
  id: z.number().int().optional(),
  url: z.union([z.string(), z.null()]).optional(),
  title: z.union([z.string(), z.null()]).optional(),
  imageUrl: z.union([z.string(), z.null()]).optional(),
  date: z.string().datetime().optional(),
})

export const zCommentResponseV2 = z.object({
  count: z.number().int().optional(),
  comments: z
    .union([
      z.array(
        z.object({
          cid: z.coerce.bigint().optional(),
          p: z.union([z.string(), z.null()]).optional(),
          m: z.union([z.string(), z.null()]).optional(),
        })
      ),
      z.null(),
    ])
    .optional(),
})

export const zCommentData = z.object({
  cid: z.coerce.bigint().optional(),
  p: z.union([z.string(), z.null()]).optional(),
  m: z.union([z.string(), z.null()]).optional(),
})

export const zSendCommentResponseV2 = zResponseBase.merge(
  z.object({
    cid: z.coerce.bigint().optional(),
  })
)

export const zSendCommentRequest = z.object({
  time: z.number().optional(),
  mode: z.number().int().optional(),
  color: z.number().int().optional(),
  comment: z.union([z.string(), z.null()]).optional(),
})

export const zUserFavoriteResponse = zResponseBase.merge(
  z.object({
    favorites: z
      .union([
        z.array(
          z.object({
            animeId: z.number().int().optional(),
            bangumiId: z.union([z.string(), z.null()]).optional(),
            animeTitle: z.union([z.string(), z.null()]).optional(),
            type: zAnimeType.optional(),
            lastFavoriteTime: z.string().datetime().optional(),
            lastAirDate: z.union([z.string().datetime(), z.null()]).optional(),
            lastWatchTime: z
              .union([z.string().datetime(), z.null()])
              .optional(),
            imageUrl: z.union([z.string(), z.null()]).optional(),
            episodeTotal: z.number().int().optional(),
            episodeWatched: z.number().int().optional(),
            startDate: z.union([z.string().datetime(), z.null()]).optional(),
            isOnAir: z.boolean().optional(),
            favoriteStatus: zFavoriteStatus.optional(),
            userRating: z.number().int().optional(),
            rating: z.number().optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zUserFavoriteItem = z.object({
  animeId: z.number().int().optional(),
  bangumiId: z.union([z.string(), z.null()]).optional(),
  animeTitle: z.union([z.string(), z.null()]).optional(),
  type: zAnimeType.optional(),
  lastFavoriteTime: z.string().datetime().optional(),
  lastAirDate: z.union([z.string().datetime(), z.null()]).optional(),
  lastWatchTime: z.union([z.string().datetime(), z.null()]).optional(),
  imageUrl: z.union([z.string(), z.null()]).optional(),
  episodeTotal: z.number().int().optional(),
  episodeWatched: z.number().int().optional(),
  startDate: z.union([z.string().datetime(), z.null()]).optional(),
  isOnAir: z.boolean().optional(),
  favoriteStatus: zFavoriteStatus.optional(),
  userRating: z.number().int().optional(),
  rating: z.number().optional(),
})

export const zUserAddFavoriteResponse = zResponseBase.merge(z.object({}))

export const zUserAddFavoriteRequest = z.object({
  animeId: z.number().int().optional(),
  favoriteStatus: z.union([zFavoriteStatus, z.null()]).optional(),
  rating: z.number().int().optional(),
  comment: z.union([z.string(), z.null()]).optional(),
})

export const zUserDeleteFavoriteResponse = zResponseBase.merge(z.object({}))

export const zHomepageResponseV2 = zResponseBase.merge(
  z.object({
    banners: z
      .union([
        z.array(
          z.object({
            id: z.number().int().optional(),
            title: z.union([z.string(), z.null()]).optional(),
            description: z.union([z.string(), z.null()]).optional(),
            url: z.union([z.string(), z.null()]).optional(),
            imageUrl: z.union([z.string(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    bangumiQueueIntroList: z
      .union([z.array(zBangumiQueueIntroV2), z.null()])
      .optional(),
    shinBangumiList: z.union([z.array(zBangumiIntro), z.null()]).optional(),
    bangumiSeasons: z.union([z.array(zBangumiSeason), z.null()]).optional(),
  })
)

export const zBannerPageItem = z.object({
  id: z.number().int().optional(),
  title: z.union([z.string(), z.null()]).optional(),
  description: z.union([z.string(), z.null()]).optional(),
  url: z.union([z.string(), z.null()]).optional(),
  imageUrl: z.union([z.string(), z.null()]).optional(),
})

export const zBannerResponse = zResponseBase.merge(
  z.object({
    banners: z.union([z.array(zBannerPageItem), z.null()]).optional(),
  })
)

export const zLoginResponse = zResponseBase.merge(
  z.object({
    registerRequired: z.boolean().optional(),
    userId: z.number().int().optional(),
    userName: z.union([z.string(), z.null()]).optional(),
    legacyTokenNumber: z.number().int().optional(),
    token: z.union([z.string(), z.null()]).optional(),
    tokenExpireTime: z.string().datetime().optional(),
    userType: z.union([z.string(), z.null()]).optional(),
    screenName: z.union([z.string(), z.null()]).optional(),
    profileImage: z.union([z.string(), z.null()]).optional(),
    appScope: z.union([z.string(), z.null()]).optional(),
    payConfigs: z
      .union([
        z.array(
          z.object({
            providerId: z.union([z.string(), z.null()]).optional(),
            providerName: z.union([z.string(), z.null()]).optional(),
            items: z
              .union([
                z.array(
                  z.object({
                    id: z.union([z.string(), z.null()]).optional(),
                    name: z.union([z.string(), z.null()]).optional(),
                    price: z.number().int().optional(),
                    currency: z.union([z.string(), z.null()]).optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    privileges: z
      .union([
        z.object({
          member: z.union([z.string().datetime(), z.null()]).optional(),
          resmonitor: z.union([z.string().datetime(), z.null()]).optional(),
        }),
        z.null(),
      ])
      .optional(),
    code: z.union([z.string(), z.null()]).optional(),
    ts: z.coerce.bigint().optional(),
  })
)

export const zPayConfig = z.object({
  providerId: z.union([z.string(), z.null()]).optional(),
  providerName: z.union([z.string(), z.null()]).optional(),
  items: z
    .union([
      z.array(
        z.object({
          id: z.union([z.string(), z.null()]).optional(),
          name: z.union([z.string(), z.null()]).optional(),
          price: z.number().int().optional(),
          currency: z.union([z.string(), z.null()]).optional(),
        })
      ),
      z.null(),
    ])
    .optional(),
})

export const zPayConfigItem = z.object({
  id: z.union([z.string(), z.null()]).optional(),
  name: z.union([z.string(), z.null()]).optional(),
  price: z.number().int().optional(),
  currency: z.union([z.string(), z.null()]).optional(),
})

export const zUserPrivileges = z.object({
  member: z.union([z.string().datetime(), z.null()]).optional(),
  resmonitor: z.union([z.string().datetime(), z.null()]).optional(),
})

export const zLoginRequest = z.object({
  userName: z.string().min(0).max(50),
  password: z.string().min(0).max(50),
  appId: z.string().min(0).max(20),
  unixTimestamp: z.coerce.bigint().gte(1).lte(9223372036854780000).optional(),
  hash: z
    .string()
    .min(1)
    .regex(/^[0-9a-fA-F]{32}$/),
})

export const zMatchResponseV2 = zResponseBase.merge(
  z.object({
    isMatched: z.boolean().optional(),
    matches: z
      .union([
        z.array(
          z.object({
            episodeId: z.coerce.bigint().optional(),
            animeId: z.number().int().optional(),
            animeTitle: z.union([z.string(), z.null()]).optional(),
            episodeTitle: z.union([z.string(), z.null()]).optional(),
            type: zAnimeType.optional(),
            typeDescription: z.union([z.string(), z.null()]).optional(),
            shift: z.number().optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zMatchResultV2 = z.object({
  episodeId: z.coerce.bigint().optional(),
  animeId: z.number().int().optional(),
  animeTitle: z.union([z.string(), z.null()]).optional(),
  episodeTitle: z.union([z.string(), z.null()]).optional(),
  type: zAnimeType.optional(),
  typeDescription: z.union([z.string(), z.null()]).optional(),
  shift: z.number().optional(),
})

export const zMatchRequest = z.object({
  fileName: z.union([z.string(), z.null()]).optional(),
  fileHash: z.union([z.string(), z.null()]).optional(),
  fileSize: z.coerce.bigint().optional(),
  videoDuration: z.number().int().optional(),
  matchMode: z.enum(['hashAndFileName', 'fileNameOnly', 'hashOnly']).optional(),
})

export const zMatchMode = z.enum([
  'hashAndFileName',
  'fileNameOnly',
  'hashOnly',
])

export const zBatchMatchResponse = zResponseBase.merge(
  z.object({
    results: z
      .union([
        z.array(
          z.object({
            success: z.boolean().optional(),
            fileHash: z.union([z.string(), z.null()]).optional(),
            matchResult: z.union([zMatchResultV2, z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zBatchMatchResponseItem = z.object({
  success: z.boolean().optional(),
  fileHash: z.union([z.string(), z.null()]).optional(),
  matchResult: z.union([zMatchResultV2, z.null()]).optional(),
})

export const zBatchMatchRequest = z.object({
  requests: z.union([z.array(zMatchRequest), z.null()]).optional(),
})

export const zUserPlayHistoryResponse = zResponseBase.merge(
  z.object({
    playHistoryAnimes: z
      .union([
        z.array(
          z.object({
            animeId: z.number().int().optional(),
            animeTitle: z.union([z.string(), z.null()]).optional(),
            type: zAnimeType.optional(),
            typeDescription: z.union([z.string(), z.null()]).optional(),
            imageUrl: z.union([z.string(), z.null()]).optional(),
            isOnAir: z.boolean().optional(),
            episodes: z.union([z.array(zBangumiEpisode), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zUserPlayHistoryAnime = z.object({
  animeId: z.number().int().optional(),
  animeTitle: z.union([z.string(), z.null()]).optional(),
  type: zAnimeType.optional(),
  typeDescription: z.union([z.string(), z.null()]).optional(),
  imageUrl: z.union([z.string(), z.null()]).optional(),
  isOnAir: z.boolean().optional(),
  episodes: z.union([z.array(zBangumiEpisode), z.null()]).optional(),
})

export const zUserAddPlayHistoryResponse = zResponseBase.merge(z.object({}))

export const zUserAddPlayHistoryRequest = z.object({
  episodeIdList: z.union([z.array(z.coerce.bigint()), z.null()]).optional(),
  addToFavorite: z.boolean().optional(),
  rating: z.number().int().optional(),
})

export const zRegisterRequestV2 = z.object({
  appId: z.string().min(1),
  userName: z.string().min(1),
  password: z.string().min(1),
  email: z.string().min(1),
  screenName: z.string().min(1),
  unixTimestamp: z.coerce.bigint().gte(1).lte(9223372036854780000).optional(),
  hash: z
    .string()
    .min(1)
    .regex(/^[0-9a-fA-F]{32}$/),
})

export const zResetPasswordResponseV2 = zResponseBase.merge(z.object({}))

export const zResetPasswordRequestV2 = z.object({
  appId: z.string().min(1),
  userName: z.string().min(5).max(20),
  email: z.string().min(1),
  unixTimestamp: z.coerce.bigint().gte(1).lte(9223372036854780000).optional(),
  hash: z
    .string()
    .min(1)
    .regex(/^[0-9a-fA-F]{32}$/),
})

export const zFindMyIdResponse = zResponseBase.merge(z.object({}))

export const zFindMyIdRequestV2 = z.object({
  appId: z.string().min(1).max(20),
  email: z.string().min(1).max(50),
  unixTimestamp: z.coerce.bigint().gte(1).lte(9223372036854780000).optional(),
  hash: z
    .string()
    .min(1)
    .regex(/^[0-9a-fA-F]{32}$/),
})

export const zRelatedResponseV2 = zResponseBase.merge(
  z.object({
    relateds: z
      .union([
        z.array(
          z.object({
            url: z.union([z.string(), z.null()]).optional(),
            shift: z.number().optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zRelatedItemV2 = z.object({
  url: z.union([z.string(), z.null()]).optional(),
  shift: z.number().optional(),
})

export const zUserAddRelatedResponse = zResponseBase.merge(z.object({}))

export const zUserAddRelatedRequest = z.object({
  episodeId: z.coerce.bigint().optional(),
  url: z.union([z.string(), z.null()]).optional(),
  shift: z.number().optional(),
})

export const zUserDeleteRelatedResponse = zResponseBase.merge(z.object({}))

export const zSearchAnimeResponse = zResponseBase.merge(
  z.object({
    animes: z
      .union([
        z.array(
          z.object({
            animeId: z.number().int().optional(),
            bangumiId: z.union([z.string(), z.null()]).optional(),
            animeTitle: z.union([z.string(), z.null()]).optional(),
            type: zAnimeType.optional(),
            typeDescription: z.union([z.string(), z.null()]).optional(),
            imageUrl: z.union([z.string(), z.null()]).optional(),
            startDate: z.union([z.string().datetime(), z.null()]).optional(),
            episodeCount: z.number().int().optional(),
            rating: z.number().optional(),
            isFavorited: z.boolean().optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zSearchAnimeDetails = z.object({
  animeId: z.number().int().optional(),
  bangumiId: z.union([z.string(), z.null()]).optional(),
  animeTitle: z.union([z.string(), z.null()]).optional(),
  type: zAnimeType.optional(),
  typeDescription: z.union([z.string(), z.null()]).optional(),
  imageUrl: z.union([z.string(), z.null()]).optional(),
  startDate: z.union([z.string().datetime(), z.null()]).optional(),
  episodeCount: z.number().int().optional(),
  rating: z.number().optional(),
  isFavorited: z.boolean().optional(),
})

export const zSearchEpisodesResponse = zResponseBase.merge(
  z.object({
    hasMore: z.boolean().optional(),
    animes: z
      .union([
        z.array(
          z.object({
            animeId: z.number().int().optional(),
            animeTitle: z.union([z.string(), z.null()]).optional(),
            type: zAnimeType.optional(),
            typeDescription: z.union([z.string(), z.null()]).optional(),
            episodes: z
              .union([
                z.array(
                  z.object({
                    episodeId: z.coerce.bigint().optional(),
                    episodeTitle: z.union([z.string(), z.null()]).optional(),
                  })
                ),
                z.null(),
              ])
              .optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zSearchEpisodesAnime = z.object({
  animeId: z.number().int().optional(),
  animeTitle: z.union([z.string(), z.null()]).optional(),
  type: zAnimeType.optional(),
  typeDescription: z.union([z.string(), z.null()]).optional(),
  episodes: z
    .union([
      z.array(
        z.object({
          episodeId: z.coerce.bigint().optional(),
          episodeTitle: z.union([z.string(), z.null()]).optional(),
        })
      ),
      z.null(),
    ])
    .optional(),
})

export const zSearchEpisodeDetails = z.object({
  episodeId: z.coerce.bigint().optional(),
  episodeTitle: z.union([z.string(), z.null()]).optional(),
})

export const zSearchAdvancedConfigResponse = zResponseBase.merge(
  z.object({
    types: z
      .union([
        z.array(
          z.object({
            key: z.number().int().optional(),
            value: z.union([z.string(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    tags: z
      .union([
        z.array(
          z.object({
            key: z.number().int().optional(),
            value: z.union([z.string(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    sorts: z
      .union([
        z.array(
          z.object({
            key: z.number().int().optional(),
            value: z.union([z.string(), z.null()]).optional(),
          })
        ),
        z.null(),
      ])
      .optional(),
    minYear: z.number().int().optional(),
    maxYear: z.number().int().optional(),
  })
)

export const zConfigKey = z.object({
  key: z.number().int().optional(),
  value: z.union([z.string(), z.null()]).optional(),
})

export const zSearchBangumiResponse = zResponseBase.merge(
  z.object({
    bangumis: z
      .union([
        z.array(
          zSearchAnimeDetails.merge(
            z.object({
              rank: z.number().int().optional(),
              searchKeyword: z.union([z.string(), z.null()]).optional(),
              isOnAir: z.boolean().optional(),
              isRestricted: z.boolean().optional(),
            })
          )
        ),
        z.null(),
      ])
      .optional(),
  })
)

export const zSearchBangumiDetails = zSearchAnimeDetails.merge(
  z.object({
    rank: z.number().int().optional(),
    searchKeyword: z.union([z.string(), z.null()]).optional(),
    isOnAir: z.boolean().optional(),
    isRestricted: z.boolean().optional(),
  })
)

export const zUserUpdateProfileResponseV2 = zResponseBase.merge(
  z.object({
    updateScreenName: z.union([z.string(), z.null()]).optional(),
    updateProfileImage: z.union([z.string(), z.null()]).optional(),
  })
)

export const zUserUpdatePasswordRequest = z.object({
  oldPassword: z.string().min(5).max(20),
  newPassword: z.string().min(5).max(20),
})

export const zUserUpdateProfileRequest = z.object({
  screenName: z.union([z.string().min(0).max(50), z.null()]).optional(),
  profileImageBase64: z.union([z.string(), z.null()]).optional(),
})

export const zUserUpdateEmailRequest = z.object({
  oldEmail: z.string().min(1),
  newEmail: z.string().min(1),
})

export const zBangumiGetShinBangumiResponse = zBangumiListResponse

export const zBangumiGetSeasonBangumiOfAnimeResponse = zBangumiListResponse

export const zBangumiGetSeasonsResponse = zBangumiSeasonListResponse

export const zBangumiGetQueueIntroResponse = zBangumiQueueIntroResponseV2

export const zBangumiGetQueueDetailsResponse = zBangumiQueueDetailsResponseV2

export const zBangumiGetBangumiDetailsResponse = zBangumiDetailsResponse

export const zCommentSendCommentResponse = zSendCommentResponseV2

export const zFavoriteGetUserFavoriteResponse = zUserFavoriteResponse

export const zFavoriteAddFavoriteResponse = zUserAddFavoriteResponse

export const zFavoriteDeleteFavoriteResponse = zUserDeleteFavoriteResponse

export const zHomepageGetHomepageResponse = zHomepageResponseV2

export const zHomepageGetBannerResponse = zBannerResponse

export const zLoginLoginResponse = zLoginResponse

export const zLoginRenewTokenResponse = zLoginResponse

export const zMatchMatchResponse = zMatchResponseV2

export const zMatchBatchMatchResponse = zBatchMatchResponse

export const zPlayHistoryGetUserPlayHistoryResponse = zUserPlayHistoryResponse

export const zPlayHistoryAddPlayHistoryResponse = zUserAddPlayHistoryResponse

export const zRegisterRegisterMainUserResponse = zLoginResponse

export const zRegisterResetPasswordResponse = zResetPasswordResponseV2

export const zRegisterFindMyIdResponse = zFindMyIdResponse

export const zRelatedDeleteRelatedResponse = zUserDeleteRelatedResponse

export const zRelatedGetRelatedResponse = zRelatedResponseV2

export const zRelatedAddRelatedResponse = zUserAddRelatedResponse

export const zSearchSearchAnimeResponse = zSearchAnimeResponse

export const zSearchSearchTmdbResponse = zSearchAnimeResponse

export const zSearchSearchEpisodesResponse = zSearchEpisodesResponse

export const zSearchSearchAnimeByTagResponse = zSearchAnimeResponse

export const zSearchGetSearchAdvConfigResponse = zSearchAdvancedConfigResponse

export const zSearchSearchAdvancedResponse = zSearchBangumiResponse

export const zUserUpdatePasswordResponse = zUserUpdateProfileResponseV2

export const zUserUpdateProfileResponse = zUserUpdateProfileResponseV2

export const zUserUpdateUserEmailResponse = zUserUpdateProfileResponseV2
